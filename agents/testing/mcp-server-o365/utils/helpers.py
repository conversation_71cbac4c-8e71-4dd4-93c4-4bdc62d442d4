"""Helper utilities for Office 365 MCP Server."""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from urllib.parse import quote

from dateutil import parser as date_parser


def parse_odata_datetime(date_string: str) -> Optional[datetime]:
    """Parse OData datetime string to Python datetime object."""
    if not date_string:
        return None
    
    try:
        # Handle various OData datetime formats
        if date_string.endswith('Z'):
            return date_parser.parse(date_string)
        elif '+' in date_string or date_string.count('-') > 2:
            return date_parser.parse(date_string)
        else:
            # Assume UTC if no timezone info
            return date_parser.parse(date_string + 'Z')
    except (ValueError, TypeError):
        return None


def format_odata_datetime(dt: datetime) -> str:
    """Format Python datetime object as OData datetime string."""
    return dt.isoformat() + 'Z' if dt.tzinfo is None else dt.isoformat()


def build_odata_filter(filters: Dict[str, Any]) -> str:
    """Build OData $filter query parameter from dictionary."""
    if not filters:
        return ""
    
    filter_parts = []
    
    for key, value in filters.items():
        if value is None:
            continue
        
        if isinstance(value, str):
            # String comparison
            filter_parts.append(f"{key} eq '{value}'")
        elif isinstance(value, bool):
            # Boolean comparison
            filter_parts.append(f"{key} eq {str(value).lower()}")
        elif isinstance(value, (int, float)):
            # Numeric comparison
            filter_parts.append(f"{key} eq {value}")
        elif isinstance(value, datetime):
            # DateTime comparison
            dt_str = format_odata_datetime(value)
            filter_parts.append(f"{key} eq {dt_str}")
        elif isinstance(value, dict):
            # Handle operators like {'gt': 10}, {'contains': 'text'}
            for op, op_value in value.items():
                if op == 'contains':
                    filter_parts.append(f"contains({key}, '{op_value}')")
                elif op == 'startswith':
                    filter_parts.append(f"startswith({key}, '{op_value}')")
                elif op == 'endswith':
                    filter_parts.append(f"endswith({key}, '{op_value}')")
                elif op in ['eq', 'ne', 'gt', 'ge', 'lt', 'le']:
                    if isinstance(op_value, str):
                        filter_parts.append(f"{key} {op} '{op_value}'")
                    else:
                        filter_parts.append(f"{key} {op} {op_value}")
    
    return " and ".join(filter_parts)


def build_odata_select(fields: List[str]) -> str:
    """Build OData $select query parameter from field list."""
    if not fields:
        return ""
    return ",".join(fields)


def build_odata_expand(expansions: List[str]) -> str:
    """Build OData $expand query parameter from expansion list."""
    if not expansions:
        return ""
    return ",".join(expansions)


def build_query_params(
    filter_dict: Optional[Dict[str, Any]] = None,
    select_fields: Optional[List[str]] = None,
    expand_fields: Optional[List[str]] = None,
    order_by: Optional[str] = None,
    top: Optional[int] = None,
    skip: Optional[int] = None
) -> Dict[str, str]:
    """Build query parameters dictionary for OData requests."""
    params = {}
    
    if filter_dict:
        filter_str = build_odata_filter(filter_dict)
        if filter_str:
            params["$filter"] = filter_str
    
    if select_fields:
        select_str = build_odata_select(select_fields)
        if select_str:
            params["$select"] = select_str
    
    if expand_fields:
        expand_str = build_odata_expand(expand_fields)
        if expand_str:
            params["$expand"] = expand_str
    
    if order_by:
        params["$orderby"] = order_by
    
    if top is not None:
        params["$top"] = str(top)
    
    if skip is not None:
        params["$skip"] = str(skip)
    
    return params


def paginate_results(
    all_results: List[Dict[str, Any]],
    page_size: int = 100,
    page_number: int = 1
) -> Dict[str, Any]:
    """Paginate a list of results."""
    start_index = (page_number - 1) * page_size
    end_index = start_index + page_size
    
    page_results = all_results[start_index:end_index]
    total_count = len(all_results)
    total_pages = (total_count + page_size - 1) // page_size
    
    return {
        "results": page_results,
        "pagination": {
            "page": page_number,
            "page_size": page_size,
            "total_count": total_count,
            "total_pages": total_pages,
            "has_next": page_number < total_pages,
            "has_previous": page_number > 1
        }
    }


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations."""
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing whitespace and dots
    sanitized = sanitized.strip(' .')
    # Ensure it's not empty
    if not sanitized:
        sanitized = "unnamed_file"
    return sanitized


def extract_id_from_url(url: str) -> Optional[str]:
    """Extract ID from SharePoint/Graph API URL."""
    # Pattern to match various ID formats in URLs
    patterns = [
        r"'([^']+)'",  # Single quotes
        r'\(([^)]+)\)',  # Parentheses
        r'/([a-f0-9-]{36})/',  # GUID format
        r'/([a-f0-9-]{36})$',  # GUID at end
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url, re.IGNORECASE)
        if match:
            return match.group(1)
    
    return None


def format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"
