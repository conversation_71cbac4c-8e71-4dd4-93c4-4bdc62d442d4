"""Exception classes for Office 365 MCP Server."""

from typing import Any, Dict, Optional


class Office365Error(Exception):
    """Base exception for Office 365 MCP Server errors."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary format."""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class AuthenticationError(Office365Error):
    """Raised when authentication fails."""
    pass


class APIError(Office365Error):
    """Raised when API requests fail."""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None
    ):
        super().__init__(message, error_code)
        self.status_code = status_code
        self.response_data = response_data or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary format."""
        result = super().to_dict()
        result.update({
            "status_code": self.status_code,
            "response_data": self.response_data
        })
        return result


class ConfigurationError(Office365Error):
    """Raised when configuration is invalid."""
    pass


class ResourceNotFoundError(Office365Error):
    """Raised when a requested resource is not found."""
    pass


class PermissionError(Office365Error):
    """Raised when user lacks permission for an operation."""
    pass


class ValidationError(Office365Error):
    """Raised when input validation fails."""
    pass


def format_error_response(error: Exception) -> Dict[str, Any]:
    """Format an exception as a standardized error response."""
    if isinstance(error, Office365Error):
        return error.to_dict()
    
    return {
        "error": error.__class__.__name__,
        "message": str(error),
        "error_code": None,
        "details": {}
    }
