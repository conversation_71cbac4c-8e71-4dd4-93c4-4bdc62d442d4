# Office 365 MCP Server Makefile

.PHONY: help install test run clean lint format check-deps

# Default target
help:
	@echo "Office 365 MCP Server - Available Commands:"
	@echo ""
	@echo "  install     - Install dependencies"
	@echo "  test        - Run test suite"
	@echo "  run         - Start the MCP server"
	@echo "  example     - Run example usage script"
	@echo "  lint        - Run code linting"
	@echo "  format      - Format code with black and isort"
	@echo "  check-deps  - Check for dependency updates"
	@echo "  clean       - Clean up temporary files"
	@echo "  setup-env   - Create .env file from template"
	@echo ""

# Install dependencies
install:
	@echo "Installing dependencies..."
	pip install -r requirements.txt

# Install development dependencies
install-dev:
	@echo "Installing development dependencies..."
	pip install -r requirements.txt
	pip install pytest pytest-asyncio black isort mypy flake8

# Run test suite
test:
	@echo "Running test suite..."
	python test_server.py

# Start the MCP server
run:
	@echo "Starting Office 365 MCP Server..."
	python server.py

# Run example usage
example:
	@echo "Running example usage..."
	python example_usage.py

# Run code linting
lint:
	@echo "Running linting..."
	flake8 --max-line-length=120 --ignore=E203,W503 .
	mypy --ignore-missing-imports .

# Format code
format:
	@echo "Formatting code..."
	black --line-length=120 .
	isort --profile black .

# Check for dependency updates
check-deps:
	@echo "Checking for dependency updates..."
	pip list --outdated

# Clean up temporary files
clean:
	@echo "Cleaning up..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +

# Create .env file from template
setup-env:
	@if [ ! -f .env ]; then \
		echo "Creating .env file from template..."; \
		cp .env.example .env; \
		echo "Please edit .env file with your configuration"; \
	else \
		echo ".env file already exists"; \
	fi

# Validate configuration
validate-config:
	@echo "Validating configuration..."
	python -c "from config import validate_config; validate_config(); print('Configuration is valid!')"

# Install as package
install-package:
	@echo "Installing as package..."
	pip install -e .

# Build package
build:
	@echo "Building package..."
	python setup.py sdist bdist_wheel

# Check code quality
quality: lint
	@echo "Running quality checks..."
	python -m py_compile server.py
	python -m py_compile config.py

# Full development setup
dev-setup: install-dev setup-env
	@echo "Development environment setup complete!"
	@echo "Please edit .env file with your Azure AD configuration"

# Quick start
quickstart: dev-setup validate-config test
	@echo "Quick start complete! You can now run 'make run' to start the server"
