#!/usr/bin/env python3
"""
Office 365 MCP Server

A modern Model Context Protocol server providing comprehensive Office 365 functionality
including Outlook, SharePoint, OneDrive, Teams, OneNote, and Planner operations.

This server is based on the office365-rest-python-client library but rewritten using
the modern MCP Python SDK for better integration with LLMs and other applications.
"""

import logging
import sys
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

from mcp.server.fastmcp import FastMCP, Context
from mcp.server.fastmcp.exceptions import McpError

# Import configuration and clients
from config import Office365Config, get_config, validate_config
from clients import GraphClient, SharePointClient
from utils.exceptions import Office365Error, format_error_response

# Import tools
from tools import (
    OutlookTools, SharePointTools, OneDriveTools, 
    TeamsTools, OneNoteTools, PlannerTools
)

# Import resources
from resources import (
    OutlookResources, SharePointResources, 
    OneDriveResources, TeamsResources
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global configuration
config = get_config()

# Application context for storing clients and tools
class AppContext:
    def __init__(self):
        self.graph_client: Optional[GraphClient] = None
        self.sharepoint_client: Optional[SharePointClient] = None
        self.outlook_tools: Optional[OutlookTools] = None
        self.sharepoint_tools: Optional[SharePointTools] = None
        self.onedrive_tools: Optional[OneDriveTools] = None
        self.teams_tools: Optional[TeamsTools] = None
        self.onenote_tools: Optional[OneNoteTools] = None
        self.planner_tools: Optional[PlannerTools] = None
        self.outlook_resources: Optional[OutlookResources] = None
        self.sharepoint_resources: Optional[SharePointResources] = None
        self.onedrive_resources: Optional[OneDriveResources] = None
        self.teams_resources: Optional[TeamsResources] = None

# Application lifespan management
@asynccontextmanager
async def app_lifespan(server: FastMCP):
    """Manage application lifecycle with type-safe context."""
    logger.info("Office 365 MCP Server starting up...")
    
    try:
        # Validate configuration
        validate_config()
        logger.info("Configuration validated successfully")
        
        # Initialize application context
        app_ctx = AppContext()
        
        # Initialize Graph client
        app_ctx.graph_client = GraphClient(config)
        logger.info("Graph client initialized")
        
        # Initialize SharePoint client if configured
        if config.sharepoint_base_url:
            app_ctx.sharepoint_client = SharePointClient(config)
            logger.info("SharePoint client initialized")
        
        # Initialize tools
        app_ctx.outlook_tools = OutlookTools(app_ctx.graph_client)
        app_ctx.onedrive_tools = OneDriveTools(app_ctx.graph_client)
        app_ctx.teams_tools = TeamsTools(app_ctx.graph_client)
        app_ctx.onenote_tools = OneNoteTools(app_ctx.graph_client)
        app_ctx.planner_tools = PlannerTools(app_ctx.graph_client)
        
        if app_ctx.sharepoint_client:
            app_ctx.sharepoint_tools = SharePointTools(app_ctx.sharepoint_client)
        
        # Initialize resources
        app_ctx.outlook_resources = OutlookResources(app_ctx.graph_client)
        app_ctx.onedrive_resources = OneDriveResources(app_ctx.graph_client)
        app_ctx.teams_resources = TeamsResources(app_ctx.graph_client)
        
        if app_ctx.sharepoint_client:
            app_ctx.sharepoint_resources = SharePointResources(app_ctx.sharepoint_client)
        
        logger.info("All tools and resources initialized")
        
        yield {"app_context": app_ctx}
        
    except Exception as e:
        logger.error(f"Failed to initialize server: {e}")
        raise
    finally:
        logger.info("Office 365 MCP Server shutting down...")
        
        # Cleanup clients
        if app_ctx.graph_client:
            await app_ctx.graph_client.close()
        
        if app_ctx.sharepoint_client:
            await app_ctx.sharepoint_client.close()
        
        logger.info("Cleanup completed")

# Create FastMCP server
mcp = FastMCP(
    name=config.server_name,
    version=config.server_version,
    lifespan=app_lifespan
)

def get_app_context(ctx: Context) -> AppContext:
    """Get application context from MCP context."""
    return ctx.request_context.lifespan_context["app_context"]

# Error handler
def handle_error(error: Exception) -> Dict[str, Any]:
    """Handle and format errors consistently."""
    logger.error(f"Error occurred: {error}")
    return format_error_response(error)

# ============================================================================
# OUTLOOK TOOLS
# ============================================================================

@mcp.tool()
async def list_messages(
    ctx: Context,
    user_id: str = "me",
    folder_name: Optional[str] = None,
    subject_filter: Optional[str] = None,
    from_filter: Optional[str] = None,
    unread_only: bool = False,
    limit: int = 50
) -> Dict[str, Any]:
    """List email messages with optional filtering.
    
    Args:
        user_id: User ID or 'me' for current user
        folder_name: Folder name (inbox, sent, drafts, etc.)
        subject_filter: Filter by subject containing text
        from_filter: Filter by sender email
        unread_only: Only return unread messages
        limit: Maximum number of messages to return
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_tools.list_messages(
            user_id, folder_name, subject_filter, from_filter, unread_only, limit
        )
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def get_message(
    ctx: Context,
    message_id: str,
    user_id: str = "me",
    include_body: bool = True
) -> Dict[str, Any]:
    """Get specific email message details.
    
    Args:
        message_id: Message ID
        user_id: User ID or 'me' for current user
        include_body: Whether to include message body
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_tools.get_message(message_id, user_id, include_body)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def send_message(
    ctx: Context,
    to_recipients: List[str],
    subject: str,
    body: str,
    cc_recipients: Optional[List[str]] = None,
    bcc_recipients: Optional[List[str]] = None,
    body_type: str = "HTML",
    user_id: str = "me"
) -> Dict[str, Any]:
    """Send email message.
    
    Args:
        to_recipients: List of recipient email addresses
        subject: Email subject
        body: Email body content
        cc_recipients: List of CC recipient email addresses
        bcc_recipients: List of BCC recipient email addresses
        body_type: Body content type (HTML or Text)
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_tools.send_message(
            to_recipients, subject, body, cc_recipients, bcc_recipients, body_type, user_id
        )
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def mark_message_as_read(
    ctx: Context,
    message_id: str,
    user_id: str = "me"
) -> Dict[str, Any]:
    """Mark message as read.
    
    Args:
        message_id: Message ID
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_tools.mark_as_read(message_id, user_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def delete_message(
    ctx: Context,
    message_id: str,
    user_id: str = "me"
) -> Dict[str, Any]:
    """Delete email message.
    
    Args:
        message_id: Message ID
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_tools.delete_message(message_id, user_id)
    except Exception as e:
        return handle_error(e)

# ============================================================================
# ONEDRIVE TOOLS
# ============================================================================

@mcp.tool()
async def get_drive_info(
    ctx: Context,
    user_id: str = "me"
) -> Dict[str, Any]:
    """Get OneDrive information.
    
    Args:
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onedrive_tools.get_drive_info(user_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def list_drive_items(
    ctx: Context,
    folder_id: str = "root",
    user_id: str = "me",
    item_type: Optional[str] = None
) -> Dict[str, Any]:
    """List items in OneDrive folder.
    
    Args:
        folder_id: Folder ID or 'root' for root folder
        user_id: User ID or 'me' for current user
        item_type: Filter by item type ('file' or 'folder')
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onedrive_tools.list_items(folder_id, user_id, item_type)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def get_drive_item(
    ctx: Context,
    item_id: str,
    user_id: str = "me"
) -> Dict[str, Any]:
    """Get specific OneDrive item details.

    Args:
        item_id: Item ID
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onedrive_tools.get_item(item_id, user_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def create_folder(
    ctx: Context,
    folder_name: str,
    parent_id: str = "root",
    user_id: str = "me"
) -> Dict[str, Any]:
    """Create folder in OneDrive.

    Args:
        folder_name: Name of the folder to create
        parent_id: Parent folder ID or 'root'
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onedrive_tools.create_folder(folder_name, parent_id, user_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def delete_drive_item(
    ctx: Context,
    item_id: str,
    user_id: str = "me"
) -> Dict[str, Any]:
    """Delete OneDrive item.

    Args:
        item_id: Item ID to delete
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onedrive_tools.delete_item(item_id, user_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def search_drive_items(
    ctx: Context,
    query: str,
    user_id: str = "me",
    limit: int = 50
) -> Dict[str, Any]:
    """Search for items in OneDrive.

    Args:
        query: Search query
        user_id: User ID or 'me' for current user
        limit: Maximum number of results
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onedrive_tools.search_items(query, user_id, limit)
    except Exception as e:
        return handle_error(e)

# ============================================================================
# TEAMS TOOLS
# ============================================================================

@mcp.tool()
async def list_teams(ctx: Context) -> Dict[str, Any]:
    """List teams the user is a member of."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.teams_tools.list_teams()
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def get_team(ctx: Context, team_id: str) -> Dict[str, Any]:
    """Get team information.

    Args:
        team_id: Team ID
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.teams_tools.get_team(team_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def list_channels(ctx: Context, team_id: str) -> Dict[str, Any]:
    """List channels in a team.

    Args:
        team_id: Team ID
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.teams_tools.list_channels(team_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def get_channel_messages(
    ctx: Context,
    team_id: str,
    channel_id: str,
    limit: int = 50
) -> Dict[str, Any]:
    """Get messages from a channel.

    Args:
        team_id: Team ID
        channel_id: Channel ID
        limit: Maximum number of messages
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.teams_tools.get_channel_messages(team_id, channel_id, limit)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def send_channel_message(
    ctx: Context,
    team_id: str,
    channel_id: str,
    message: str,
    subject: Optional[str] = None
) -> Dict[str, Any]:
    """Send message to a channel.

    Args:
        team_id: Team ID
        channel_id: Channel ID
        message: Message content
        subject: Optional message subject
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.teams_tools.send_channel_message(team_id, channel_id, message, subject)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def list_team_members(ctx: Context, team_id: str) -> Dict[str, Any]:
    """List members of a team.

    Args:
        team_id: Team ID
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.teams_tools.list_team_members(team_id)
    except Exception as e:
        return handle_error(e)

# ============================================================================
# SHAREPOINT TOOLS (if configured)
# ============================================================================

@mcp.tool()
async def get_sharepoint_site_info(ctx: Context) -> Dict[str, Any]:
    """Get SharePoint site information."""
    try:
        app_ctx = get_app_context(ctx)
        if not app_ctx.sharepoint_tools:
            raise McpError("SharePoint not configured")
        return await app_ctx.sharepoint_tools.get_site_info()
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def list_sharepoint_lists(ctx: Context) -> Dict[str, Any]:
    """List all SharePoint lists."""
    try:
        app_ctx = get_app_context(ctx)
        if not app_ctx.sharepoint_tools:
            raise McpError("SharePoint not configured")
        return await app_ctx.sharepoint_tools.list_lists()
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def get_sharepoint_list_items(
    ctx: Context,
    list_name: str,
    limit: int = 100,
    filter_field: Optional[str] = None,
    filter_value: Optional[str] = None
) -> Dict[str, Any]:
    """Get items from a SharePoint list.

    Args:
        list_name: List name or ID
        limit: Maximum number of items to return
        filter_field: Field name to filter by
        filter_value: Value to filter by
    """
    try:
        app_ctx = get_app_context(ctx)
        if not app_ctx.sharepoint_tools:
            raise McpError("SharePoint not configured")
        return await app_ctx.sharepoint_tools.get_list_items(list_name, limit, filter_field, filter_value)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def create_sharepoint_list_item(
    ctx: Context,
    list_name: str,
    item_data: Dict[str, Any]
) -> Dict[str, Any]:
    """Create item in SharePoint list.

    Args:
        list_name: List name or ID
        item_data: Item data to create
    """
    try:
        app_ctx = get_app_context(ctx)
        if not app_ctx.sharepoint_tools:
            raise McpError("SharePoint not configured")
        return await app_ctx.sharepoint_tools.create_list_item(list_name, item_data)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def list_sharepoint_files(
    ctx: Context,
    folder_path: str = ""
) -> Dict[str, Any]:
    """List files in SharePoint folder.

    Args:
        folder_path: Folder path (empty for root)
    """
    try:
        app_ctx = get_app_context(ctx)
        if not app_ctx.sharepoint_tools:
            raise McpError("SharePoint not configured")
        return await app_ctx.sharepoint_tools.list_files(folder_path)
    except Exception as e:
        return handle_error(e)

# ============================================================================
# ONENOTE TOOLS
# ============================================================================

@mcp.tool()
async def list_notebooks(ctx: Context, user_id: str = "me") -> Dict[str, Any]:
    """List OneNote notebooks.

    Args:
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onenote_tools.list_notebooks(user_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def list_onenote_pages(
    ctx: Context,
    section_id: Optional[str] = None,
    notebook_id: Optional[str] = None,
    user_id: str = "me",
    limit: int = 50
) -> Dict[str, Any]:
    """List pages in a section, notebook, or all pages.

    Args:
        section_id: Section ID (optional)
        notebook_id: Notebook ID (optional, used if section_id not provided)
        user_id: User ID or 'me' for current user
        limit: Maximum number of pages
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onenote_tools.list_pages(section_id, notebook_id, user_id, limit)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def create_onenote_page(
    ctx: Context,
    section_id: str,
    title: str,
    content: str,
    user_id: str = "me"
) -> Dict[str, Any]:
    """Create new OneNote page.

    Args:
        section_id: Section ID where to create the page
        title: Page title
        content: Page content (HTML)
        user_id: User ID or 'me' for current user
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onenote_tools.create_page(section_id, title, content, user_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def search_onenote_pages(
    ctx: Context,
    query: str,
    user_id: str = "me",
    limit: int = 50
) -> Dict[str, Any]:
    """Search for OneNote pages.

    Args:
        query: Search query
        user_id: User ID or 'me' for current user
        limit: Maximum number of results
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onenote_tools.search_pages(query, user_id, limit)
    except Exception as e:
        return handle_error(e)

# ============================================================================
# PLANNER TOOLS
# ============================================================================

@mcp.tool()
async def list_planner_plans(ctx: Context, group_id: Optional[str] = None) -> Dict[str, Any]:
    """List Planner plans.

    Args:
        group_id: Group ID to filter plans (optional)
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.planner_tools.list_plans(group_id)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def list_planner_tasks(
    ctx: Context,
    plan_id: Optional[str] = None,
    bucket_id: Optional[str] = None,
    assigned_to: Optional[str] = None
) -> Dict[str, Any]:
    """List tasks in a plan or bucket.

    Args:
        plan_id: Plan ID (optional)
        bucket_id: Bucket ID (optional)
        assigned_to: User ID to filter by assignee (optional)
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.planner_tools.list_tasks(plan_id, bucket_id, assigned_to)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def create_planner_task(
    ctx: Context,
    plan_id: str,
    title: str,
    bucket_id: Optional[str] = None,
    due_date: Optional[str] = None,
    assigned_to: Optional[List[str]] = None,
    priority: int = 5
) -> Dict[str, Any]:
    """Create new Planner task.

    Args:
        plan_id: Plan ID
        title: Task title
        bucket_id: Bucket ID (optional)
        due_date: Due date in ISO format (optional)
        assigned_to: List of user IDs to assign (optional)
        priority: Task priority (1-10, default 5)
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.planner_tools.create_task(plan_id, title, bucket_id, due_date, assigned_to, priority)
    except Exception as e:
        return handle_error(e)

@mcp.tool()
async def update_planner_task(
    ctx: Context,
    task_id: str,
    title: Optional[str] = None,
    due_date: Optional[str] = None,
    percent_complete: Optional[int] = None,
    priority: Optional[int] = None
) -> Dict[str, Any]:
    """Update Planner task.

    Args:
        task_id: Task ID
        title: New title (optional)
        due_date: New due date (optional)
        percent_complete: Completion percentage (optional)
        priority: New priority (optional)
    """
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.planner_tools.update_task(task_id, title, due_date, percent_complete, priority)
    except Exception as e:
        return handle_error(e)

# ============================================================================
# RESOURCES
# ============================================================================

@mcp.resource("outlook://profile/{user_id}")
async def get_user_profile_resource(ctx: Context, user_id: str = "me") -> str:
    """Get user profile information as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_resources.get_user_profile(user_id)
    except Exception as e:
        logger.error(f"Error getting user profile resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("outlook://mailbox/{user_id}")
async def get_mailbox_summary_resource(ctx: Context, user_id: str = "me") -> str:
    """Get mailbox summary as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_resources.get_mailbox_summary(user_id)
    except Exception as e:
        logger.error(f"Error getting mailbox summary resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("outlook://calendar/{user_id}")
async def get_calendar_summary_resource(ctx: Context, user_id: str = "me") -> str:
    """Get calendar summary as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_resources.get_calendar_summary(user_id)
    except Exception as e:
        logger.error(f"Error getting calendar summary resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("outlook://message/{message_id}")
async def get_message_content_resource(ctx: Context, message_id: str, user_id: str = "me") -> str:
    """Get specific message content as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.outlook_resources.get_message_content(message_id, user_id)
    except Exception as e:
        logger.error(f"Error getting message content resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("onedrive://overview/{user_id}")
async def get_drive_overview_resource(ctx: Context, user_id: str = "me") -> str:
    """Get OneDrive overview as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onedrive_resources.get_drive_overview(user_id)
    except Exception as e:
        logger.error(f"Error getting drive overview resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("onedrive://folder/{folder_id}")
async def get_folder_contents_resource(ctx: Context, folder_id: str = "root", user_id: str = "me") -> str:
    """Get folder contents as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.onedrive_resources.get_folder_contents(folder_id, user_id)
    except Exception as e:
        logger.error(f"Error getting folder contents resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("teams://overview")
async def get_teams_overview_resource(ctx: Context) -> str:
    """Get Teams overview as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.teams_resources.get_teams_overview()
    except Exception as e:
        logger.error(f"Error getting Teams overview resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("teams://team/{team_id}")
async def get_team_details_resource(ctx: Context, team_id: str) -> str:
    """Get team details as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        return await app_ctx.teams_resources.get_team_details(team_id)
    except Exception as e:
        logger.error(f"Error getting team details resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("sharepoint://site")
async def get_sharepoint_site_overview_resource(ctx: Context) -> str:
    """Get SharePoint site overview as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        if not app_ctx.sharepoint_resources:
            return "SharePoint not configured"
        return await app_ctx.sharepoint_resources.get_site_overview()
    except Exception as e:
        logger.error(f"Error getting SharePoint site overview resource: {e}")
        return f"Error: {str(e)}"

@mcp.resource("sharepoint://list/{list_name}")
async def get_sharepoint_list_schema_resource(ctx: Context, list_name: str) -> str:
    """Get SharePoint list schema as a resource."""
    try:
        app_ctx = get_app_context(ctx)
        if not app_ctx.sharepoint_resources:
            return "SharePoint not configured"
        return await app_ctx.sharepoint_resources.get_list_schema(list_name)
    except Exception as e:
        logger.error(f"Error getting SharePoint list schema resource: {e}")
        return f"Error: {str(e)}"

# ============================================================================
# MAIN EXECUTION
# ============================================================================

if __name__ == "__main__":
    logger.info("Starting Office 365 MCP Server...")

    try:
        # Validate configuration before starting
        validate_config()
        logger.info("Configuration validation successful")

        # Run the server
        mcp.run()

    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)
