# MCP Server for Office 365
# Modern Context Protocol server providing Office 365 functionality

# Core MCP dependencies
mcp[cli]>=1.9.0

# HTTP client for API requests
httpx>=0.27.0

# Authentication and cryptography
msal>=1.24.0
cryptography>=41.0.0
PyJWT>=2.8.0

# Data handling
pydantic>=2.0.0
python-dateutil>=2.8.0

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
mypy>=1.5.0

# Optional: For certificate authentication
certifi>=2023.7.22
