#!/usr/bin/env python3
"""
Test script for Office 365 MCP Server

This script tests the MCP server functionality without requiring a full MCP client.
"""

import asyncio
import os
import sys
from typing import Any, Dict

# Add the server directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Office365Config, validate_config
from server import mcp, AppContext


class MockContext:
    """Mock MCP context for testing."""
    
    def __init__(self, app_context: AppContext):
        self.request_context = MockRequestContext(app_context)


class MockRequestContext:
    """Mock request context."""
    
    def __init__(self, app_context: AppContext):
        self.lifespan_context = {"app_context": app_context}


async def test_server_initialization():
    """Test server initialization."""
    print("Testing server initialization...")
    
    try:
        # Validate configuration
        validate_config()
        print("✓ Configuration validation passed")
        
        # Test app context creation
        from server import AppContext
        from clients import GraphClient, SharePointClient
        from tools import OutlookTools, OneDriveTools, TeamsTools, OneNoteTools, PlannerTools
        from resources import OutlookResources, OneDriveResources, TeamsResources
        
        config = Office365Config()
        app_ctx = AppContext()
        
        # Initialize clients
        app_ctx.graph_client = GraphClient(config)
        print("✓ Graph client initialized")
        
        if config.sharepoint_base_url:
            app_ctx.sharepoint_client = SharePointClient(config)
            print("✓ SharePoint client initialized")
        
        # Initialize tools
        app_ctx.outlook_tools = OutlookTools(app_ctx.graph_client)
        app_ctx.onedrive_tools = OneDriveTools(app_ctx.graph_client)
        app_ctx.teams_tools = TeamsTools(app_ctx.graph_client)
        app_ctx.onenote_tools = OneNoteTools(app_ctx.graph_client)
        app_ctx.planner_tools = PlannerTools(app_ctx.graph_client)
        print("✓ All tools initialized")
        
        # Initialize resources
        app_ctx.outlook_resources = OutlookResources(app_ctx.graph_client)
        app_ctx.onedrive_resources = OneDriveResources(app_ctx.graph_client)
        app_ctx.teams_resources = TeamsResources(app_ctx.graph_client)
        print("✓ All resources initialized")
        
        return app_ctx
        
    except Exception as e:
        print(f"✗ Initialization failed: {e}")
        raise


async def test_tools(app_ctx: AppContext):
    """Test MCP tools."""
    print("\nTesting MCP tools...")
    
    try:
        # Create mock context
        ctx = MockContext(app_ctx)
        
        # Import tool functions from server
        from server import (
            get_drive_info, list_drive_items, list_teams,
            list_messages, get_user_profile_resource
        )
        
        # Test OneDrive tool
        print("Testing get_drive_info tool...")
        drive_result = await get_drive_info(ctx)
        if "error" not in drive_result:
            print(f"✓ Drive info: {drive_result.get('name', 'Unknown')}")
        else:
            print(f"✗ Drive info error: {drive_result['message']}")
        
        # Test Teams tool
        print("Testing list_teams tool...")
        teams_result = await list_teams(ctx)
        if "error" not in teams_result:
            print(f"✓ Found {teams_result.get('count', 0)} teams")
        else:
            print(f"✗ Teams error: {teams_result['message']}")
        
        # Test Outlook tool
        print("Testing list_messages tool...")
        messages_result = await list_messages(ctx, limit=5)
        if "error" not in messages_result:
            print(f"✓ Found {messages_result.get('count', 0)} messages")
        else:
            print(f"✗ Messages error: {messages_result['message']}")
        
        print("✓ Tool testing completed")
        
    except Exception as e:
        print(f"✗ Tool testing failed: {e}")


async def test_resources(app_ctx: AppContext):
    """Test MCP resources."""
    print("\nTesting MCP resources...")
    
    try:
        # Create mock context
        ctx = MockContext(app_ctx)
        
        # Import resource functions from server
        from server import (
            get_user_profile_resource, get_drive_overview_resource,
            get_teams_overview_resource
        )
        
        # Test user profile resource
        print("Testing user profile resource...")
        profile_result = await get_user_profile_resource(ctx)
        if not profile_result.startswith("Error:"):
            print("✓ User profile resource working")
        else:
            print(f"✗ Profile resource error: {profile_result}")
        
        # Test drive overview resource
        print("Testing drive overview resource...")
        drive_result = await get_drive_overview_resource(ctx)
        if not drive_result.startswith("Error:"):
            print("✓ Drive overview resource working")
        else:
            print(f"✗ Drive resource error: {drive_result}")
        
        # Test teams overview resource
        print("Testing teams overview resource...")
        teams_result = await get_teams_overview_resource(ctx)
        if not teams_result.startswith("Error:"):
            print("✓ Teams overview resource working")
        else:
            print(f"✗ Teams resource error: {teams_result}")
        
        print("✓ Resource testing completed")
        
    except Exception as e:
        print(f"✗ Resource testing failed: {e}")


async def cleanup(app_ctx: AppContext):
    """Cleanup resources."""
    print("\nCleaning up...")
    
    try:
        if app_ctx.graph_client:
            await app_ctx.graph_client.close()
        
        if app_ctx.sharepoint_client:
            await app_ctx.sharepoint_client.close()
        
        print("✓ Cleanup completed")
        
    except Exception as e:
        print(f"✗ Cleanup error: {e}")


async def main():
    """Main test function."""
    print("Office 365 MCP Server - Test Suite")
    print("=" * 50)
    
    app_ctx = None
    
    try:
        # Test initialization
        app_ctx = await test_server_initialization()
        
        # Test tools
        await test_tools(app_ctx)
        
        # Test resources
        await test_resources(app_ctx)
        
        print("\n" + "=" * 50)
        print("✓ All tests completed successfully!")
        
        return 0
        
    except Exception as e:
        print(f"\n✗ Test suite failed: {e}")
        print("\nPlease ensure you have:")
        print("1. Set all required environment variables")
        print("2. Configured Azure AD app with proper permissions")
        print("3. Granted admin consent for the permissions")
        
        return 1
        
    finally:
        if app_ctx:
            await cleanup(app_ctx)


if __name__ == "__main__":
    # Run the test suite
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
