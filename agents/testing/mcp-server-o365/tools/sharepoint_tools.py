"""SharePoint tools for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.sharepoint_client import SharePointClient
from ..utils.exceptions import APIError, ValidationError
from ..utils.helpers import sanitize_filename, format_file_size

logger = logging.getLogger(__name__)


class SharePointTools:
    """Tools for SharePoint operations."""
    
    def __init__(self, sharepoint_client: SharePointClient):
        """Initialize SharePoint tools.
        
        Args:
            sharepoint_client: SharePoint client
        """
        self.client = sharepoint_client
    
    async def get_site_info(self) -> Dict[str, Any]:
        """Get SharePoint site information.
        
        Returns:
            Site information
        """
        try:
            logger.info("SharePointTools.get_site_info - Getting site information")
            
            web_info = await self.client.get_web()
            web_props = await self.client.get_web_properties()
            
            return {
                "title": web_info.get("d", {}).get("Title", ""),
                "description": web_info.get("d", {}).get("Description", ""),
                "url": web_info.get("d", {}).get("Url", ""),
                "server_relative_url": web_info.get("d", {}).get("ServerRelativeUrl", ""),
                "created": web_info.get("d", {}).get("Created", ""),
                "last_modified": web_info.get("d", {}).get("LastItemModifiedDate", ""),
                "language": web_info.get("d", {}).get("Language", ""),
                "web_template": web_info.get("d", {}).get("WebTemplate", ""),
                "properties": web_props.get("d", {}).get("results", [])
            }
            
        except Exception as e:
            logger.error(f"Error getting site info: {e}")
            raise APIError(f"Failed to get site information: {str(e)}")
    
    async def list_lists(self) -> Dict[str, Any]:
        """List all SharePoint lists.
        
        Returns:
            List of SharePoint lists
        """
        try:
            logger.info("SharePointTools.list_lists - Listing SharePoint lists")
            
            response = await self.client.get_lists()
            lists_data = response.get("d", {}).get("results", [])
            
            formatted_lists = []
            for list_item in lists_data:
                formatted_list = {
                    "id": list_item.get("Id"),
                    "title": list_item.get("Title"),
                    "description": list_item.get("Description"),
                    "item_count": list_item.get("ItemCount", 0),
                    "created": list_item.get("Created"),
                    "last_modified": list_item.get("LastItemModifiedDate"),
                    "list_template": list_item.get("BaseTemplate"),
                    "hidden": list_item.get("Hidden", False),
                    "default_view_url": list_item.get("DefaultViewUrl")
                }
                formatted_lists.append(formatted_list)
            
            return {
                "lists": formatted_lists,
                "count": len(formatted_lists)
            }
            
        except Exception as e:
            logger.error(f"Error listing lists: {e}")
            raise APIError(f"Failed to list SharePoint lists: {str(e)}")
    
    async def get_list_items(
        self,
        list_name: str,
        limit: int = 100,
        filter_field: Optional[str] = None,
        filter_value: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get items from a SharePoint list.
        
        Args:
            list_name: List name or ID
            limit: Maximum number of items to return
            filter_field: Field name to filter by
            filter_value: Value to filter by
            
        Returns:
            List items
        """
        try:
            logger.info(f"SharePointTools.get_list_items - Getting items from list {list_name}")
            
            filter_dict = None
            if filter_field and filter_value:
                filter_dict = {filter_field: filter_value}
            
            response = await self.client.get_list_items(
                list_id=list_name,
                filter_dict=filter_dict,
                top=limit
            )
            
            items_data = response.get("d", {}).get("results", [])
            
            formatted_items = []
            for item in items_data:
                # Extract common fields
                formatted_item = {
                    "id": item.get("Id"),
                    "title": item.get("Title"),
                    "created": item.get("Created"),
                    "modified": item.get("Modified"),
                    "author": item.get("Author", {}).get("Title") if item.get("Author") else None,
                    "editor": item.get("Editor", {}).get("Title") if item.get("Editor") else None
                }
                
                # Add all other fields
                for key, value in item.items():
                    if key not in ["Id", "Title", "Created", "Modified", "Author", "Editor"]:
                        formatted_item[key] = value
                
                formatted_items.append(formatted_item)
            
            return {
                "items": formatted_items,
                "count": len(formatted_items),
                "list_name": list_name
            }
            
        except Exception as e:
            logger.error(f"Error getting list items: {e}")
            raise APIError(f"Failed to get list items: {str(e)}")
    
    async def create_list_item(
        self,
        list_name: str,
        item_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create item in SharePoint list.
        
        Args:
            list_name: List name or ID
            item_data: Item data to create
            
        Returns:
            Created item information
        """
        try:
            logger.info(f"SharePointTools.create_list_item - Creating item in list {list_name}")
            
            if not item_data:
                raise ValidationError("Item data is required")
            
            response = await self.client.create_list_item(
                list_id=list_name,
                item_data=item_data
            )
            
            created_item = response.get("d", {})
            
            return {
                "success": True,
                "item_id": created_item.get("Id"),
                "title": created_item.get("Title"),
                "created": created_item.get("Created"),
                "list_name": list_name
            }
            
        except Exception as e:
            logger.error(f"Error creating list item: {e}")
            raise APIError(f"Failed to create list item: {str(e)}")
    
    async def update_list_item(
        self,
        list_name: str,
        item_id: int,
        item_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update item in SharePoint list.
        
        Args:
            list_name: List name or ID
            item_id: Item ID to update
            item_data: Updated item data
            
        Returns:
            Update confirmation
        """
        try:
            logger.info(f"SharePointTools.update_list_item - Updating item {item_id} in list {list_name}")
            
            if not item_data:
                raise ValidationError("Item data is required")
            
            await self.client.update_list_item(
                list_id=list_name,
                item_id=item_id,
                item_data=item_data
            )
            
            return {
                "success": True,
                "message": "Item updated successfully",
                "item_id": item_id,
                "list_name": list_name
            }
            
        except Exception as e:
            logger.error(f"Error updating list item: {e}")
            raise APIError(f"Failed to update list item: {str(e)}")
    
    async def delete_list_item(
        self,
        list_name: str,
        item_id: int
    ) -> Dict[str, Any]:
        """Delete item from SharePoint list.
        
        Args:
            list_name: List name or ID
            item_id: Item ID to delete
            
        Returns:
            Delete confirmation
        """
        try:
            logger.info(f"SharePointTools.delete_list_item - Deleting item {item_id} from list {list_name}")
            
            await self.client.delete_list_item(
                list_id=list_name,
                item_id=item_id
            )
            
            return {
                "success": True,
                "message": "Item deleted successfully",
                "item_id": item_id,
                "list_name": list_name
            }
            
        except Exception as e:
            logger.error(f"Error deleting list item: {e}")
            raise APIError(f"Failed to delete list item: {str(e)}")
    
    async def list_files(
        self,
        folder_path: str = ""
    ) -> Dict[str, Any]:
        """List files in SharePoint folder.
        
        Args:
            folder_path: Folder path (empty for root)
            
        Returns:
            List of files
        """
        try:
            logger.info(f"SharePointTools.list_files - Listing files in folder {folder_path or 'root'}")
            
            response = await self.client.get_files(folder_path)
            files_data = response.get("d", {}).get("results", [])
            
            formatted_files = []
            for file_item in files_data:
                formatted_file = {
                    "name": file_item.get("Name"),
                    "server_relative_url": file_item.get("ServerRelativeUrl"),
                    "size": file_item.get("Length", 0),
                    "size_formatted": format_file_size(file_item.get("Length", 0)),
                    "created": file_item.get("TimeCreated"),
                    "modified": file_item.get("TimeLastModified"),
                    "title": file_item.get("Title"),
                    "file_type": file_item.get("Name", "").split(".")[-1] if "." in file_item.get("Name", "") else ""
                }
                formatted_files.append(formatted_file)
            
            return {
                "files": formatted_files,
                "count": len(formatted_files),
                "folder_path": folder_path or "root"
            }
            
        except Exception as e:
            logger.error(f"Error listing files: {e}")
            raise APIError(f"Failed to list files: {str(e)}")
    
    async def get_file_info(
        self,
        file_path: str
    ) -> Dict[str, Any]:
        """Get file information.
        
        Args:
            file_path: File server relative path
            
        Returns:
            File information
        """
        try:
            logger.info(f"SharePointTools.get_file_info - Getting info for file {file_path}")
            
            response = await self.client.get_file(file_path)
            file_data = response.get("d", {})
            
            return {
                "name": file_data.get("Name"),
                "title": file_data.get("Title"),
                "server_relative_url": file_data.get("ServerRelativeUrl"),
                "size": file_data.get("Length", 0),
                "size_formatted": format_file_size(file_data.get("Length", 0)),
                "created": file_data.get("TimeCreated"),
                "modified": file_data.get("TimeLastModified"),
                "author": file_data.get("Author", {}).get("Title") if file_data.get("Author") else None,
                "modified_by": file_data.get("ModifiedBy", {}).get("Title") if file_data.get("ModifiedBy") else None,
                "file_type": file_data.get("Name", "").split(".")[-1] if "." in file_data.get("Name", "") else "",
                "check_out_type": file_data.get("CheckOutType"),
                "version": file_data.get("UIVersionLabel")
            }
            
        except Exception as e:
            logger.error(f"Error getting file info: {e}")
            raise APIError(f"Failed to get file information: {str(e)}")
