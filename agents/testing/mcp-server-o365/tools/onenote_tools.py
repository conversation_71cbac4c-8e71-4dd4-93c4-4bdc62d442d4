"""OneNote tools for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.graph_client import GraphClient
from ..utils.exceptions import APIError, ValidationError

logger = logging.getLogger(__name__)


class OneNoteTools:
    """Tools for Microsoft OneNote operations."""
    
    def __init__(self, graph_client: GraphClient):
        """Initialize OneNote tools.
        
        Args:
            graph_client: Microsoft Graph client
        """
        self.client = graph_client
    
    async def list_notebooks(self, user_id: str = "me") -> Dict[str, Any]:
        """List OneNote notebooks.
        
        Args:
            user_id: User ID or 'me' for current user
            
        Returns:
            List of notebooks
        """
        try:
            logger.info(f"OneNoteTools.list_notebooks - Listing notebooks for user {user_id}")
            
            response = await self.client.get(f"users/{user_id}/onenote/notebooks")
            notebooks_data = response.get("value", [])
            
            formatted_notebooks = []
            for notebook in notebooks_data:
                formatted_notebook = {
                    "id": notebook.get("id"),
                    "display_name": notebook.get("displayName"),
                    "created_date": notebook.get("createdDateTime"),
                    "last_modified": notebook.get("lastModifiedDateTime"),
                    "is_default": notebook.get("isDefault", False),
                    "is_shared": notebook.get("isShared", False),
                    "sections_url": notebook.get("sectionsUrl"),
                    "section_groups_url": notebook.get("sectionGroupsUrl"),
                    "web_url": notebook.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                    "created_by": notebook.get("createdBy", {}).get("user", {}).get("displayName")
                }
                formatted_notebooks.append(formatted_notebook)
            
            return {
                "notebooks": formatted_notebooks,
                "count": len(formatted_notebooks),
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error listing notebooks: {e}")
            raise APIError(f"Failed to list notebooks: {str(e)}")
    
    async def get_notebook(self, notebook_id: str, user_id: str = "me") -> Dict[str, Any]:
        """Get specific notebook.
        
        Args:
            notebook_id: Notebook ID
            user_id: User ID or 'me' for current user
            
        Returns:
            Notebook details
        """
        try:
            logger.info(f"OneNoteTools.get_notebook - Getting notebook {notebook_id}")
            
            response = await self.client.get(f"users/{user_id}/onenote/notebooks/{notebook_id}")
            
            return {
                "id": response.get("id"),
                "display_name": response.get("displayName"),
                "created_date": response.get("createdDateTime"),
                "last_modified": response.get("lastModifiedDateTime"),
                "is_default": response.get("isDefault", False),
                "is_shared": response.get("isShared", False),
                "sections_url": response.get("sectionsUrl"),
                "section_groups_url": response.get("sectionGroupsUrl"),
                "web_url": response.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                "created_by": response.get("createdBy", {}).get("user", {}).get("displayName")
            }
            
        except Exception as e:
            logger.error(f"Error getting notebook: {e}")
            raise APIError(f"Failed to get notebook: {str(e)}")
    
    async def list_sections(
        self,
        notebook_id: Optional[str] = None,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """List sections in a notebook or all sections.
        
        Args:
            notebook_id: Notebook ID (optional, lists all sections if not provided)
            user_id: User ID or 'me' for current user
            
        Returns:
            List of sections
        """
        try:
            logger.info(f"OneNoteTools.list_sections - Listing sections")
            
            if notebook_id:
                endpoint = f"users/{user_id}/onenote/notebooks/{notebook_id}/sections"
            else:
                endpoint = f"users/{user_id}/onenote/sections"
            
            response = await self.client.get(endpoint)
            sections_data = response.get("value", [])
            
            formatted_sections = []
            for section in sections_data:
                formatted_section = {
                    "id": section.get("id"),
                    "display_name": section.get("displayName"),
                    "created_date": section.get("createdDateTime"),
                    "last_modified": section.get("lastModifiedDateTime"),
                    "is_default": section.get("isDefault", False),
                    "pages_url": section.get("pagesUrl"),
                    "web_url": section.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                    "parent_notebook": section.get("parentNotebook", {}).get("displayName"),
                    "created_by": section.get("createdBy", {}).get("user", {}).get("displayName")
                }
                formatted_sections.append(formatted_section)
            
            return {
                "sections": formatted_sections,
                "count": len(formatted_sections),
                "notebook_id": notebook_id,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error listing sections: {e}")
            raise APIError(f"Failed to list sections: {str(e)}")
    
    async def list_pages(
        self,
        section_id: Optional[str] = None,
        notebook_id: Optional[str] = None,
        user_id: str = "me",
        limit: int = 50
    ) -> Dict[str, Any]:
        """List pages in a section, notebook, or all pages.
        
        Args:
            section_id: Section ID (optional)
            notebook_id: Notebook ID (optional, used if section_id not provided)
            user_id: User ID or 'me' for current user
            limit: Maximum number of pages
            
        Returns:
            List of pages
        """
        try:
            logger.info(f"OneNoteTools.list_pages - Listing pages")
            
            if section_id:
                endpoint = f"users/{user_id}/onenote/sections/{section_id}/pages"
            elif notebook_id:
                endpoint = f"users/{user_id}/onenote/notebooks/{notebook_id}/pages"
            else:
                endpoint = f"users/{user_id}/onenote/pages"
            
            params = {"$top": limit, "$orderby": "lastModifiedDateTime desc"}
            
            response = await self.client.get(endpoint, params=params)
            pages_data = response.get("value", [])
            
            formatted_pages = []
            for page in pages_data:
                formatted_page = {
                    "id": page.get("id"),
                    "title": page.get("title"),
                    "created_date": page.get("createdDateTime"),
                    "last_modified": page.get("lastModifiedDateTime"),
                    "content_url": page.get("contentUrl"),
                    "web_url": page.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                    "parent_section": page.get("parentSection", {}).get("displayName"),
                    "parent_notebook": page.get("parentNotebook", {}).get("displayName"),
                    "created_by": page.get("createdBy", {}).get("user", {}).get("displayName"),
                    "level": page.get("level", 0)
                }
                formatted_pages.append(formatted_page)
            
            return {
                "pages": formatted_pages,
                "count": len(formatted_pages),
                "section_id": section_id,
                "notebook_id": notebook_id,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error listing pages: {e}")
            raise APIError(f"Failed to list pages: {str(e)}")
    
    async def get_page(self, page_id: str, user_id: str = "me") -> Dict[str, Any]:
        """Get specific page.
        
        Args:
            page_id: Page ID
            user_id: User ID or 'me' for current user
            
        Returns:
            Page details
        """
        try:
            logger.info(f"OneNoteTools.get_page - Getting page {page_id}")
            
            response = await self.client.get(f"users/{user_id}/onenote/pages/{page_id}")
            
            return {
                "id": response.get("id"),
                "title": response.get("title"),
                "created_date": response.get("createdDateTime"),
                "last_modified": response.get("lastModifiedDateTime"),
                "content_url": response.get("contentUrl"),
                "web_url": response.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                "parent_section": response.get("parentSection", {}).get("displayName"),
                "parent_notebook": response.get("parentNotebook", {}).get("displayName"),
                "created_by": response.get("createdBy", {}).get("user", {}).get("displayName"),
                "level": response.get("level", 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting page: {e}")
            raise APIError(f"Failed to get page: {str(e)}")
    
    async def get_page_content(self, page_id: str, user_id: str = "me") -> Dict[str, Any]:
        """Get page content.
        
        Args:
            page_id: Page ID
            user_id: User ID or 'me' for current user
            
        Returns:
            Page content
        """
        try:
            logger.info(f"OneNoteTools.get_page_content - Getting content for page {page_id}")
            
            # Get page content (HTML)
            response = await self.client.get(
                f"users/{user_id}/onenote/pages/{page_id}/content",
                headers={"Accept": "text/html"}
            )
            
            return {
                "page_id": page_id,
                "content": response.get("content", ""),
                "content_type": "html"
            }
            
        except Exception as e:
            logger.error(f"Error getting page content: {e}")
            raise APIError(f"Failed to get page content: {str(e)}")
    
    async def create_page(
        self,
        section_id: str,
        title: str,
        content: str,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Create new page.
        
        Args:
            section_id: Section ID where to create the page
            title: Page title
            content: Page content (HTML)
            user_id: User ID or 'me' for current user
            
        Returns:
            Created page information
        """
        try:
            logger.info(f"OneNoteTools.create_page - Creating page '{title}' in section {section_id}")
            
            if not title:
                raise ValidationError("Page title is required")
            
            if not content:
                raise ValidationError("Page content is required")
            
            # Create HTML content for the page
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>{title}</title>
            </head>
            <body>
                {content}
            </body>
            </html>
            """
            
            headers = {
                "Content-Type": "text/html"
            }
            
            response = await self.client.post(
                f"users/{user_id}/onenote/sections/{section_id}/pages",
                data=html_content,
                headers=headers
            )
            
            return {
                "success": True,
                "page_id": response.get("id"),
                "title": response.get("title"),
                "web_url": response.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                "content_url": response.get("contentUrl"),
                "created_date": response.get("createdDateTime"),
                "section_id": section_id
            }
            
        except Exception as e:
            logger.error(f"Error creating page: {e}")
            raise APIError(f"Failed to create page: {str(e)}")
    
    async def search_pages(
        self,
        query: str,
        user_id: str = "me",
        limit: int = 50
    ) -> Dict[str, Any]:
        """Search for pages.
        
        Args:
            query: Search query
            user_id: User ID or 'me' for current user
            limit: Maximum number of results
            
        Returns:
            Search results
        """
        try:
            logger.info(f"OneNoteTools.search_pages - Searching for '{query}'")
            
            if not query:
                raise ValidationError("Search query is required")
            
            params = {
                "search": query,
                "$top": limit
            }
            
            response = await self.client.get(
                f"users/{user_id}/onenote/pages",
                params=params
            )
            
            pages_data = response.get("value", [])
            
            formatted_pages = []
            for page in pages_data:
                formatted_page = {
                    "id": page.get("id"),
                    "title": page.get("title"),
                    "created_date": page.get("createdDateTime"),
                    "last_modified": page.get("lastModifiedDateTime"),
                    "web_url": page.get("links", {}).get("oneNoteWebUrl", {}).get("href"),
                    "parent_section": page.get("parentSection", {}).get("displayName"),
                    "parent_notebook": page.get("parentNotebook", {}).get("displayName")
                }
                formatted_pages.append(formatted_page)
            
            return {
                "pages": formatted_pages,
                "count": len(formatted_pages),
                "query": query,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error searching pages: {e}")
            raise APIError(f"Failed to search pages: {str(e)}")
