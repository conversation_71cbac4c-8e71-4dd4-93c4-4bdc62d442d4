"""Outlook tools for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.graph_client import GraphClient
from ..utils.exceptions import APIError, ValidationError
from ..utils.helpers import parse_odata_datetime, format_odata_datetime

logger = logging.getLogger(__name__)


class OutlookTools:
    """Tools for Outlook operations."""
    
    def __init__(self, graph_client: GraphClient):
        """Initialize Outlook tools.
        
        Args:
            graph_client: Microsoft Graph client
        """
        self.client = graph_client
    
    async def list_messages(
        self,
        user_id: str = "me",
        folder_name: Optional[str] = None,
        subject_filter: Optional[str] = None,
        from_filter: Optional[str] = None,
        unread_only: bool = False,
        limit: int = 50
    ) -> Dict[str, Any]:
        """List email messages.
        
        Args:
            user_id: User ID or 'me' for current user
            folder_name: Folder name (inbox, sent, drafts, etc.)
            subject_filter: Filter by subject containing text
            from_filter: Filter by sender email
            unread_only: Only return unread messages
            limit: Maximum number of messages to return
            
        Returns:
            Dictionary containing messages and metadata
        """
        try:
            logger.info(f"OutlookTools.list_messages - Listing messages for user {user_id}")
            
            # Build filter conditions
            filter_conditions = []
            
            if subject_filter:
                filter_conditions.append(f"contains(subject, '{subject_filter}')")
            
            if from_filter:
                filter_conditions.append(f"from/emailAddress/address eq '{from_filter}'")
            
            if unread_only:
                filter_conditions.append("isRead eq false")
            
            filter_dict = None
            if filter_conditions:
                filter_dict = {"$filter": " and ".join(filter_conditions)}
            
            # Map folder names to folder IDs if needed
            folder_id = None
            if folder_name:
                folder_mapping = {
                    "inbox": "inbox",
                    "sent": "sentitems",
                    "drafts": "drafts",
                    "deleted": "deleteditems",
                    "junk": "junkemail"
                }
                folder_id = folder_mapping.get(folder_name.lower(), folder_name)
            
            # Select relevant fields
            select_fields = [
                "id", "subject", "from", "toRecipients", "ccRecipients",
                "receivedDateTime", "sentDateTime", "isRead", "importance",
                "hasAttachments", "bodyPreview"
            ]
            
            response = await self.client.list_messages(
                user_id=user_id,
                folder_id=folder_id,
                filter_dict=filter_dict,
                select_fields=select_fields,
                top=limit
            )
            
            messages = response.get("value", [])
            
            # Format messages for better readability
            formatted_messages = []
            for msg in messages:
                formatted_msg = {
                    "id": msg.get("id"),
                    "subject": msg.get("subject"),
                    "from": msg.get("from", {}).get("emailAddress", {}).get("address"),
                    "from_name": msg.get("from", {}).get("emailAddress", {}).get("name"),
                    "to_recipients": [
                        recipient.get("emailAddress", {}).get("address")
                        for recipient in msg.get("toRecipients", [])
                    ],
                    "received_date": msg.get("receivedDateTime"),
                    "sent_date": msg.get("sentDateTime"),
                    "is_read": msg.get("isRead", False),
                    "importance": msg.get("importance", "normal"),
                    "has_attachments": msg.get("hasAttachments", False),
                    "preview": msg.get("bodyPreview", "")[:200] + "..." if len(msg.get("bodyPreview", "")) > 200 else msg.get("bodyPreview", "")
                }
                formatted_messages.append(formatted_msg)
            
            return {
                "messages": formatted_messages,
                "count": len(formatted_messages),
                "folder": folder_name or "all",
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error listing messages: {e}")
            raise APIError(f"Failed to list messages: {str(e)}")
    
    async def get_message(
        self,
        message_id: str,
        user_id: str = "me",
        include_body: bool = True
    ) -> Dict[str, Any]:
        """Get specific email message.
        
        Args:
            message_id: Message ID
            user_id: User ID or 'me' for current user
            include_body: Whether to include message body
            
        Returns:
            Message details
        """
        try:
            logger.info(f"OutlookTools.get_message - Getting message {message_id}")
            
            select_fields = [
                "id", "subject", "from", "toRecipients", "ccRecipients", "bccRecipients",
                "receivedDateTime", "sentDateTime", "isRead", "importance",
                "hasAttachments", "internetMessageId", "conversationId"
            ]
            
            if include_body:
                select_fields.append("body")
            
            response = await self.client.get_message(message_id, user_id)
            
            # Format response
            formatted_message = {
                "id": response.get("id"),
                "subject": response.get("subject"),
                "from": {
                    "address": response.get("from", {}).get("emailAddress", {}).get("address"),
                    "name": response.get("from", {}).get("emailAddress", {}).get("name")
                },
                "to_recipients": [
                    {
                        "address": recipient.get("emailAddress", {}).get("address"),
                        "name": recipient.get("emailAddress", {}).get("name")
                    }
                    for recipient in response.get("toRecipients", [])
                ],
                "cc_recipients": [
                    {
                        "address": recipient.get("emailAddress", {}).get("address"),
                        "name": recipient.get("emailAddress", {}).get("name")
                    }
                    for recipient in response.get("ccRecipients", [])
                ],
                "received_date": response.get("receivedDateTime"),
                "sent_date": response.get("sentDateTime"),
                "is_read": response.get("isRead", False),
                "importance": response.get("importance", "normal"),
                "has_attachments": response.get("hasAttachments", False),
                "conversation_id": response.get("conversationId"),
                "internet_message_id": response.get("internetMessageId")
            }
            
            if include_body and "body" in response:
                formatted_message["body"] = {
                    "content": response["body"].get("content", ""),
                    "content_type": response["body"].get("contentType", "text")
                }
            
            return formatted_message
            
        except Exception as e:
            logger.error(f"Error getting message: {e}")
            raise APIError(f"Failed to get message: {str(e)}")
    
    async def send_message(
        self,
        to_recipients: List[str],
        subject: str,
        body: str,
        cc_recipients: Optional[List[str]] = None,
        bcc_recipients: Optional[List[str]] = None,
        body_type: str = "HTML",
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Send email message.
        
        Args:
            to_recipients: List of recipient email addresses
            subject: Email subject
            body: Email body content
            cc_recipients: List of CC recipient email addresses
            bcc_recipients: List of BCC recipient email addresses
            body_type: Body content type (HTML or Text)
            user_id: User ID or 'me' for current user
            
        Returns:
            Success confirmation
        """
        try:
            logger.info(f"OutlookTools.send_message - Sending message to {len(to_recipients)} recipients")
            
            if not to_recipients:
                raise ValidationError("At least one recipient is required")
            
            if not subject:
                raise ValidationError("Subject is required")
            
            if not body:
                raise ValidationError("Body is required")
            
            response = await self.client.send_message(
                to_recipients=to_recipients,
                subject=subject,
                body=body,
                cc_recipients=cc_recipients,
                bcc_recipients=bcc_recipients,
                user_id=user_id
            )
            
            return {
                "success": True,
                "message": "Email sent successfully",
                "to_recipients": to_recipients,
                "subject": subject,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise APIError(f"Failed to send message: {str(e)}")
    
    async def mark_as_read(
        self,
        message_id: str,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Mark message as read.
        
        Args:
            message_id: Message ID
            user_id: User ID or 'me' for current user
            
        Returns:
            Success confirmation
        """
        try:
            logger.info(f"OutlookTools.mark_as_read - Marking message {message_id} as read")
            
            await self.client.patch(
                f"users/{user_id}/messages/{message_id}",
                data={"isRead": True}
            )
            
            return {
                "success": True,
                "message": "Message marked as read",
                "message_id": message_id
            }
            
        except Exception as e:
            logger.error(f"Error marking message as read: {e}")
            raise APIError(f"Failed to mark message as read: {str(e)}")
    
    async def delete_message(
        self,
        message_id: str,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Delete message.
        
        Args:
            message_id: Message ID
            user_id: User ID or 'me' for current user
            
        Returns:
            Success confirmation
        """
        try:
            logger.info(f"OutlookTools.delete_message - Deleting message {message_id}")
            
            await self.client.delete(f"users/{user_id}/messages/{message_id}")
            
            return {
                "success": True,
                "message": "Message deleted successfully",
                "message_id": message_id
            }
            
        except Exception as e:
            logger.error(f"Error deleting message: {e}")
            raise APIError(f"Failed to delete message: {str(e)}")
