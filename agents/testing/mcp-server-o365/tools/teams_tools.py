"""Teams tools for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.graph_client import GraphClient
from ..utils.exceptions import APIError, ValidationError

logger = logging.getLogger(__name__)


class TeamsTools:
    """Tools for Microsoft Teams operations."""
    
    def __init__(self, graph_client: GraphClient):
        """Initialize Teams tools.
        
        Args:
            graph_client: Microsoft Graph client
        """
        self.client = graph_client
    
    async def list_teams(self) -> Dict[str, Any]:
        """List teams the user is a member of.
        
        Returns:
            List of teams
        """
        try:
            logger.info("TeamsTools.list_teams - Listing user's teams")
            
            response = await self.client.list_teams()
            teams_data = response.get("value", [])
            
            formatted_teams = []
            for team in teams_data:
                formatted_team = {
                    "id": team.get("id"),
                    "display_name": team.get("displayName"),
                    "description": team.get("description"),
                    "visibility": team.get("visibility"),
                    "web_url": team.get("webUrl"),
                    "created_date": team.get("createdDateTime"),
                    "archived": team.get("isArchived", False)
                }
                formatted_teams.append(formatted_team)
            
            return {
                "teams": formatted_teams,
                "count": len(formatted_teams)
            }
            
        except Exception as e:
            logger.error(f"Error listing teams: {e}")
            raise APIError(f"Failed to list teams: {str(e)}")
    
    async def get_team(self, team_id: str) -> Dict[str, Any]:
        """Get team information.
        
        Args:
            team_id: Team ID
            
        Returns:
            Team details
        """
        try:
            logger.info(f"TeamsTools.get_team - Getting team {team_id}")
            
            response = await self.client.get_team(team_id)
            
            return {
                "id": response.get("id"),
                "display_name": response.get("displayName"),
                "description": response.get("description"),
                "visibility": response.get("visibility"),
                "web_url": response.get("webUrl"),
                "created_date": response.get("createdDateTime"),
                "archived": response.get("isArchived", False),
                "member_settings": response.get("memberSettings", {}),
                "guest_settings": response.get("guestSettings", {}),
                "messaging_settings": response.get("messagingSettings", {}),
                "fun_settings": response.get("funSettings", {})
            }
            
        except Exception as e:
            logger.error(f"Error getting team: {e}")
            raise APIError(f"Failed to get team: {str(e)}")
    
    async def list_channels(self, team_id: str) -> Dict[str, Any]:
        """List channels in a team.
        
        Args:
            team_id: Team ID
            
        Returns:
            List of channels
        """
        try:
            logger.info(f"TeamsTools.list_channels - Listing channels for team {team_id}")
            
            response = await self.client.list_channels(team_id)
            channels_data = response.get("value", [])
            
            formatted_channels = []
            for channel in channels_data:
                formatted_channel = {
                    "id": channel.get("id"),
                    "display_name": channel.get("displayName"),
                    "description": channel.get("description"),
                    "email": channel.get("email"),
                    "web_url": channel.get("webUrl"),
                    "membership_type": channel.get("membershipType"),
                    "created_date": channel.get("createdDateTime")
                }
                formatted_channels.append(formatted_channel)
            
            return {
                "channels": formatted_channels,
                "count": len(formatted_channels),
                "team_id": team_id
            }
            
        except Exception as e:
            logger.error(f"Error listing channels: {e}")
            raise APIError(f"Failed to list channels: {str(e)}")
    
    async def get_channel_messages(
        self,
        team_id: str,
        channel_id: str,
        limit: int = 50
    ) -> Dict[str, Any]:
        """Get messages from a channel.
        
        Args:
            team_id: Team ID
            channel_id: Channel ID
            limit: Maximum number of messages
            
        Returns:
            Channel messages
        """
        try:
            logger.info(f"TeamsTools.get_channel_messages - Getting messages from channel {channel_id}")
            
            params = {"$top": limit, "$orderby": "createdDateTime desc"}
            
            response = await self.client.get(
                f"teams/{team_id}/channels/{channel_id}/messages",
                params=params
            )
            
            messages_data = response.get("value", [])
            
            formatted_messages = []
            for message in messages_data:
                formatted_message = {
                    "id": message.get("id"),
                    "created_date": message.get("createdDateTime"),
                    "last_modified": message.get("lastModifiedDateTime"),
                    "message_type": message.get("messageType"),
                    "importance": message.get("importance"),
                    "subject": message.get("subject"),
                    "summary": message.get("summary"),
                    "from": message.get("from", {}).get("user", {}).get("displayName") if message.get("from") else None,
                    "web_url": message.get("webUrl"),
                    "reply_count": len(message.get("replies", []))
                }
                
                # Extract body content
                body = message.get("body", {})
                if body:
                    formatted_message["body"] = {
                        "content": body.get("content", ""),
                        "content_type": body.get("contentType", "text")
                    }
                
                formatted_messages.append(formatted_message)
            
            return {
                "messages": formatted_messages,
                "count": len(formatted_messages),
                "team_id": team_id,
                "channel_id": channel_id
            }
            
        except Exception as e:
            logger.error(f"Error getting channel messages: {e}")
            raise APIError(f"Failed to get channel messages: {str(e)}")
    
    async def send_channel_message(
        self,
        team_id: str,
        channel_id: str,
        message: str,
        subject: Optional[str] = None
    ) -> Dict[str, Any]:
        """Send message to a channel.
        
        Args:
            team_id: Team ID
            channel_id: Channel ID
            message: Message content
            subject: Optional message subject
            
        Returns:
            Sent message confirmation
        """
        try:
            logger.info(f"TeamsTools.send_channel_message - Sending message to channel {channel_id}")
            
            if not message:
                raise ValidationError("Message content is required")
            
            message_data = {
                "body": {
                    "contentType": "html",
                    "content": message
                }
            }
            
            if subject:
                message_data["subject"] = subject
            
            response = await self.client.post(
                f"teams/{team_id}/channels/{channel_id}/messages",
                data=message_data
            )
            
            return {
                "success": True,
                "message_id": response.get("id"),
                "created_date": response.get("createdDateTime"),
                "web_url": response.get("webUrl"),
                "team_id": team_id,
                "channel_id": channel_id
            }
            
        except Exception as e:
            logger.error(f"Error sending channel message: {e}")
            raise APIError(f"Failed to send channel message: {str(e)}")
    
    async def list_team_members(self, team_id: str) -> Dict[str, Any]:
        """List members of a team.
        
        Args:
            team_id: Team ID
            
        Returns:
            Team members
        """
        try:
            logger.info(f"TeamsTools.list_team_members - Listing members for team {team_id}")
            
            response = await self.client.get(f"teams/{team_id}/members")
            members_data = response.get("value", [])
            
            formatted_members = []
            for member in members_data:
                formatted_member = {
                    "id": member.get("id"),
                    "display_name": member.get("displayName"),
                    "email": member.get("email"),
                    "roles": member.get("roles", []),
                    "user_id": member.get("userId"),
                    "tenant_id": member.get("tenantId")
                }
                formatted_members.append(formatted_member)
            
            return {
                "members": formatted_members,
                "count": len(formatted_members),
                "team_id": team_id
            }
            
        except Exception as e:
            logger.error(f"Error listing team members: {e}")
            raise APIError(f"Failed to list team members: {str(e)}")
    
    async def get_team_apps(self, team_id: str) -> Dict[str, Any]:
        """Get apps installed in a team.
        
        Args:
            team_id: Team ID
            
        Returns:
            Team apps
        """
        try:
            logger.info(f"TeamsTools.get_team_apps - Getting apps for team {team_id}")
            
            response = await self.client.get(f"teams/{team_id}/installedApps")
            apps_data = response.get("value", [])
            
            formatted_apps = []
            for app in apps_data:
                formatted_app = {
                    "id": app.get("id"),
                    "teams_app_id": app.get("teamsApp", {}).get("id"),
                    "display_name": app.get("teamsApp", {}).get("displayName"),
                    "distribution_method": app.get("teamsApp", {}).get("distributionMethod"),
                    "external_id": app.get("teamsApp", {}).get("externalId")
                }
                formatted_apps.append(formatted_app)
            
            return {
                "apps": formatted_apps,
                "count": len(formatted_apps),
                "team_id": team_id
            }
            
        except Exception as e:
            logger.error(f"Error getting team apps: {e}")
            raise APIError(f"Failed to get team apps: {str(e)}")
