"""OneDrive tools for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.graph_client import GraphClient
from ..utils.exceptions import APIError, ValidationError
from ..utils.helpers import format_file_size, sanitize_filename

logger = logging.getLogger(__name__)


class OneDriveTools:
    """Tools for OneDrive operations."""
    
    def __init__(self, graph_client: GraphClient):
        """Initialize OneDrive tools.
        
        Args:
            graph_client: Microsoft Graph client
        """
        self.client = graph_client
    
    async def get_drive_info(
        self,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Get OneDrive information.
        
        Args:
            user_id: User ID or 'me' for current user
            
        Returns:
            Drive information
        """
        try:
            logger.info(f"OneDriveTools.get_drive_info - Getting drive info for user {user_id}")
            
            response = await self.client.get_drive(user_id)
            
            return {
                "id": response.get("id"),
                "name": response.get("name"),
                "drive_type": response.get("driveType"),
                "owner": response.get("owner", {}).get("user", {}).get("displayName"),
                "quota": {
                    "total": response.get("quota", {}).get("total", 0),
                    "used": response.get("quota", {}).get("used", 0),
                    "remaining": response.get("quota", {}).get("remaining", 0),
                    "deleted": response.get("quota", {}).get("deleted", 0),
                    "total_formatted": format_file_size(response.get("quota", {}).get("total", 0)),
                    "used_formatted": format_file_size(response.get("quota", {}).get("used", 0)),
                    "remaining_formatted": format_file_size(response.get("quota", {}).get("remaining", 0))
                },
                "web_url": response.get("webUrl")
            }
            
        except Exception as e:
            logger.error(f"Error getting drive info: {e}")
            raise APIError(f"Failed to get drive information: {str(e)}")
    
    async def list_items(
        self,
        folder_id: str = "root",
        user_id: str = "me",
        item_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """List items in OneDrive folder.
        
        Args:
            folder_id: Folder ID or 'root' for root folder
            user_id: User ID or 'me' for current user
            item_type: Filter by item type ('file' or 'folder')
            
        Returns:
            List of drive items
        """
        try:
            logger.info(f"OneDriveTools.list_items - Listing items in folder {folder_id}")
            
            select_fields = [
                "id", "name", "size", "createdDateTime", "lastModifiedDateTime",
                "file", "folder", "webUrl", "parentReference", "createdBy", "lastModifiedBy"
            ]
            
            response = await self.client.list_drive_items(
                item_id=folder_id,
                user_id=user_id,
                select_fields=select_fields
            )
            
            items_data = response.get("value", [])
            
            formatted_items = []
            for item in items_data:
                # Determine item type
                is_file = "file" in item
                is_folder = "folder" in item
                
                # Apply type filter if specified
                if item_type:
                    if item_type.lower() == "file" and not is_file:
                        continue
                    elif item_type.lower() == "folder" and not is_folder:
                        continue
                
                formatted_item = {
                    "id": item.get("id"),
                    "name": item.get("name"),
                    "type": "file" if is_file else "folder",
                    "size": item.get("size", 0),
                    "size_formatted": format_file_size(item.get("size", 0)),
                    "created": item.get("createdDateTime"),
                    "modified": item.get("lastModifiedDateTime"),
                    "web_url": item.get("webUrl"),
                    "parent_id": item.get("parentReference", {}).get("id"),
                    "created_by": item.get("createdBy", {}).get("user", {}).get("displayName"),
                    "modified_by": item.get("lastModifiedBy", {}).get("user", {}).get("displayName")
                }
                
                # Add file-specific information
                if is_file:
                    file_info = item.get("file", {})
                    formatted_item.update({
                        "mime_type": file_info.get("mimeType"),
                        "file_extension": item.get("name", "").split(".")[-1] if "." in item.get("name", "") else ""
                    })
                
                # Add folder-specific information
                if is_folder:
                    folder_info = item.get("folder", {})
                    formatted_item.update({
                        "child_count": folder_info.get("childCount", 0)
                    })
                
                formatted_items.append(formatted_item)
            
            return {
                "items": formatted_items,
                "count": len(formatted_items),
                "folder_id": folder_id,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error listing drive items: {e}")
            raise APIError(f"Failed to list drive items: {str(e)}")
    
    async def get_item(
        self,
        item_id: str,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Get specific OneDrive item.
        
        Args:
            item_id: Item ID
            user_id: User ID or 'me' for current user
            
        Returns:
            Item details
        """
        try:
            logger.info(f"OneDriveTools.get_item - Getting item {item_id}")
            
            response = await self.client.get_drive_item(item_id, user_id)
            
            is_file = "file" in response
            is_folder = "folder" in response
            
            formatted_item = {
                "id": response.get("id"),
                "name": response.get("name"),
                "type": "file" if is_file else "folder",
                "size": response.get("size", 0),
                "size_formatted": format_file_size(response.get("size", 0)),
                "created": response.get("createdDateTime"),
                "modified": response.get("lastModifiedDateTime"),
                "web_url": response.get("webUrl"),
                "download_url": response.get("@microsoft.graph.downloadUrl"),
                "parent_id": response.get("parentReference", {}).get("id"),
                "parent_path": response.get("parentReference", {}).get("path"),
                "created_by": response.get("createdBy", {}).get("user", {}).get("displayName"),
                "modified_by": response.get("lastModifiedBy", {}).get("user", {}).get("displayName")
            }
            
            # Add file-specific information
            if is_file:
                file_info = response.get("file", {})
                formatted_item.update({
                    "mime_type": file_info.get("mimeType"),
                    "file_extension": response.get("name", "").split(".")[-1] if "." in response.get("name", "") else "",
                    "hashes": file_info.get("hashes", {})
                })
            
            # Add folder-specific information
            if is_folder:
                folder_info = response.get("folder", {})
                formatted_item.update({
                    "child_count": folder_info.get("childCount", 0)
                })
            
            return formatted_item
            
        except Exception as e:
            logger.error(f"Error getting drive item: {e}")
            raise APIError(f"Failed to get drive item: {str(e)}")
    
    async def create_folder(
        self,
        folder_name: str,
        parent_id: str = "root",
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Create folder in OneDrive.
        
        Args:
            folder_name: Name of the folder to create
            parent_id: Parent folder ID or 'root'
            user_id: User ID or 'me' for current user
            
        Returns:
            Created folder information
        """
        try:
            logger.info(f"OneDriveTools.create_folder - Creating folder {folder_name}")
            
            if not folder_name:
                raise ValidationError("Folder name is required")
            
            # Sanitize folder name
            sanitized_name = sanitize_filename(folder_name)
            
            folder_data = {
                "name": sanitized_name,
                "folder": {},
                "@microsoft.graph.conflictBehavior": "rename"
            }
            
            response = await self.client.post(
                f"users/{user_id}/drive/items/{parent_id}/children",
                data=folder_data
            )
            
            return {
                "success": True,
                "folder_id": response.get("id"),
                "name": response.get("name"),
                "web_url": response.get("webUrl"),
                "parent_id": parent_id,
                "created": response.get("createdDateTime")
            }
            
        except Exception as e:
            logger.error(f"Error creating folder: {e}")
            raise APIError(f"Failed to create folder: {str(e)}")
    
    async def delete_item(
        self,
        item_id: str,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Delete OneDrive item.
        
        Args:
            item_id: Item ID to delete
            user_id: User ID or 'me' for current user
            
        Returns:
            Delete confirmation
        """
        try:
            logger.info(f"OneDriveTools.delete_item - Deleting item {item_id}")
            
            await self.client.delete(f"users/{user_id}/drive/items/{item_id}")
            
            return {
                "success": True,
                "message": "Item deleted successfully",
                "item_id": item_id
            }
            
        except Exception as e:
            logger.error(f"Error deleting item: {e}")
            raise APIError(f"Failed to delete item: {str(e)}")
    
    async def search_items(
        self,
        query: str,
        user_id: str = "me",
        limit: int = 50
    ) -> Dict[str, Any]:
        """Search for items in OneDrive.
        
        Args:
            query: Search query
            user_id: User ID or 'me' for current user
            limit: Maximum number of results
            
        Returns:
            Search results
        """
        try:
            logger.info(f"OneDriveTools.search_items - Searching for '{query}'")
            
            if not query:
                raise ValidationError("Search query is required")
            
            params = {
                "q": query,
                "$top": limit
            }
            
            response = await self.client.get(
                f"users/{user_id}/drive/search(q='{query}')",
                params=params
            )
            
            items_data = response.get("value", [])
            
            formatted_items = []
            for item in items_data:
                is_file = "file" in item
                
                formatted_item = {
                    "id": item.get("id"),
                    "name": item.get("name"),
                    "type": "file" if is_file else "folder",
                    "size": item.get("size", 0),
                    "size_formatted": format_file_size(item.get("size", 0)),
                    "modified": item.get("lastModifiedDateTime"),
                    "web_url": item.get("webUrl"),
                    "parent_path": item.get("parentReference", {}).get("path")
                }
                
                if is_file:
                    formatted_item["file_extension"] = item.get("name", "").split(".")[-1] if "." in item.get("name", "") else ""
                
                formatted_items.append(formatted_item)
            
            return {
                "items": formatted_items,
                "count": len(formatted_items),
                "query": query,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error searching items: {e}")
            raise APIError(f"Failed to search items: {str(e)}")
