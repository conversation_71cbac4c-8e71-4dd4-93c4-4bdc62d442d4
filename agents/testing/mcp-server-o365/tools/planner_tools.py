"""Planner tools for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.graph_client import GraphClient
from ..utils.exceptions import APIError, ValidationError

logger = logging.getLogger(__name__)


class PlannerTools:
    """Tools for Microsoft Planner operations."""
    
    def __init__(self, graph_client: GraphClient):
        """Initialize Planner tools.
        
        Args:
            graph_client: Microsoft Graph client
        """
        self.client = graph_client
    
    async def list_plans(self, group_id: Optional[str] = None) -> Dict[str, Any]:
        """List Planner plans.
        
        Args:
            group_id: Group ID to filter plans (optional)
            
        Returns:
            List of plans
        """
        try:
            logger.info("PlannerTools.list_plans - Listing Planner plans")
            
            if group_id:
                endpoint = f"groups/{group_id}/planner/plans"
            else:
                endpoint = "me/planner/plans"
            
            response = await self.client.get(endpoint)
            plans_data = response.get("value", [])
            
            formatted_plans = []
            for plan in plans_data:
                formatted_plan = {
                    "id": plan.get("id"),
                    "title": plan.get("title"),
                    "created_date": plan.get("createdDateTime"),
                    "owner": plan.get("owner"),
                    "created_by": plan.get("createdBy", {}).get("user", {}).get("displayName"),
                    "container": plan.get("container", {}),
                    "contexts": plan.get("contexts", {})
                }
                formatted_plans.append(formatted_plan)
            
            return {
                "plans": formatted_plans,
                "count": len(formatted_plans),
                "group_id": group_id
            }
            
        except Exception as e:
            logger.error(f"Error listing plans: {e}")
            raise APIError(f"Failed to list plans: {str(e)}")
    
    async def get_plan(self, plan_id: str) -> Dict[str, Any]:
        """Get specific plan.
        
        Args:
            plan_id: Plan ID
            
        Returns:
            Plan details
        """
        try:
            logger.info(f"PlannerTools.get_plan - Getting plan {plan_id}")
            
            response = await self.client.get(f"planner/plans/{plan_id}")
            
            return {
                "id": response.get("id"),
                "title": response.get("title"),
                "created_date": response.get("createdDateTime"),
                "owner": response.get("owner"),
                "created_by": response.get("createdBy", {}).get("user", {}).get("displayName"),
                "container": response.get("container", {}),
                "contexts": response.get("contexts", {})
            }
            
        except Exception as e:
            logger.error(f"Error getting plan: {e}")
            raise APIError(f"Failed to get plan: {str(e)}")
    
    async def list_buckets(self, plan_id: str) -> Dict[str, Any]:
        """List buckets in a plan.
        
        Args:
            plan_id: Plan ID
            
        Returns:
            List of buckets
        """
        try:
            logger.info(f"PlannerTools.list_buckets - Listing buckets for plan {plan_id}")
            
            response = await self.client.get(f"planner/plans/{plan_id}/buckets")
            buckets_data = response.get("value", [])
            
            formatted_buckets = []
            for bucket in buckets_data:
                formatted_bucket = {
                    "id": bucket.get("id"),
                    "name": bucket.get("name"),
                    "plan_id": bucket.get("planId"),
                    "order_hint": bucket.get("orderHint")
                }
                formatted_buckets.append(formatted_bucket)
            
            return {
                "buckets": formatted_buckets,
                "count": len(formatted_buckets),
                "plan_id": plan_id
            }
            
        except Exception as e:
            logger.error(f"Error listing buckets: {e}")
            raise APIError(f"Failed to list buckets: {str(e)}")
    
    async def list_tasks(
        self,
        plan_id: Optional[str] = None,
        bucket_id: Optional[str] = None,
        assigned_to: Optional[str] = None
    ) -> Dict[str, Any]:
        """List tasks in a plan or bucket.
        
        Args:
            plan_id: Plan ID (optional)
            bucket_id: Bucket ID (optional)
            assigned_to: User ID to filter by assignee (optional)
            
        Returns:
            List of tasks
        """
        try:
            logger.info("PlannerTools.list_tasks - Listing tasks")
            
            if bucket_id:
                endpoint = f"planner/buckets/{bucket_id}/tasks"
            elif plan_id:
                endpoint = f"planner/plans/{plan_id}/tasks"
            else:
                endpoint = "me/planner/tasks"
            
            response = await self.client.get(endpoint)
            tasks_data = response.get("value", [])
            
            # Filter by assigned user if specified
            if assigned_to:
                tasks_data = [
                    task for task in tasks_data
                    if assigned_to in task.get("assignments", {})
                ]
            
            formatted_tasks = []
            for task in tasks_data:
                formatted_task = {
                    "id": task.get("id"),
                    "title": task.get("title"),
                    "created_date": task.get("createdDateTime"),
                    "due_date": task.get("dueDateTime"),
                    "start_date": task.get("startDateTime"),
                    "completed_date": task.get("completedDateTime"),
                    "percent_complete": task.get("percentComplete", 0),
                    "priority": task.get("priority", 5),
                    "plan_id": task.get("planId"),
                    "bucket_id": task.get("bucketId"),
                    "created_by": task.get("createdBy", {}).get("user", {}).get("displayName"),
                    "assignments": list(task.get("assignments", {}).keys()),
                    "checklist_items": len(task.get("checklistItems", {})),
                    "has_description": bool(task.get("hasDescription", False)),
                    "preview_type": task.get("previewType"),
                    "reference_count": task.get("referenceCount", 0),
                    "order_hint": task.get("orderHint")
                }
                formatted_tasks.append(formatted_task)
            
            return {
                "tasks": formatted_tasks,
                "count": len(formatted_tasks),
                "plan_id": plan_id,
                "bucket_id": bucket_id,
                "assigned_to": assigned_to
            }
            
        except Exception as e:
            logger.error(f"Error listing tasks: {e}")
            raise APIError(f"Failed to list tasks: {str(e)}")
    
    async def get_task(self, task_id: str) -> Dict[str, Any]:
        """Get specific task.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task details
        """
        try:
            logger.info(f"PlannerTools.get_task - Getting task {task_id}")
            
            response = await self.client.get(f"planner/tasks/{task_id}")
            
            return {
                "id": response.get("id"),
                "title": response.get("title"),
                "created_date": response.get("createdDateTime"),
                "due_date": response.get("dueDateTime"),
                "start_date": response.get("startDateTime"),
                "completed_date": response.get("completedDateTime"),
                "percent_complete": response.get("percentComplete", 0),
                "priority": response.get("priority", 5),
                "plan_id": response.get("planId"),
                "bucket_id": response.get("bucketId"),
                "created_by": response.get("createdBy", {}).get("user", {}).get("displayName"),
                "assignments": response.get("assignments", {}),
                "checklist_items": response.get("checklistItems", {}),
                "has_description": response.get("hasDescription", False),
                "preview_type": response.get("previewType"),
                "reference_count": response.get("referenceCount", 0),
                "order_hint": response.get("orderHint"),
                "conversation_thread_id": response.get("conversationThreadId")
            }
            
        except Exception as e:
            logger.error(f"Error getting task: {e}")
            raise APIError(f"Failed to get task: {str(e)}")
    
    async def create_task(
        self,
        plan_id: str,
        title: str,
        bucket_id: Optional[str] = None,
        due_date: Optional[str] = None,
        assigned_to: Optional[List[str]] = None,
        priority: int = 5
    ) -> Dict[str, Any]:
        """Create new task.
        
        Args:
            plan_id: Plan ID
            title: Task title
            bucket_id: Bucket ID (optional)
            due_date: Due date in ISO format (optional)
            assigned_to: List of user IDs to assign (optional)
            priority: Task priority (1-10, default 5)
            
        Returns:
            Created task information
        """
        try:
            logger.info(f"PlannerTools.create_task - Creating task '{title}' in plan {plan_id}")
            
            if not title:
                raise ValidationError("Task title is required")
            
            task_data = {
                "planId": plan_id,
                "title": title,
                "priority": priority
            }
            
            if bucket_id:
                task_data["bucketId"] = bucket_id
            
            if due_date:
                task_data["dueDateTime"] = due_date
            
            if assigned_to:
                assignments = {}
                for user_id in assigned_to:
                    assignments[user_id] = {
                        "@odata.type": "microsoft.graph.plannerAssignment",
                        "orderHint": " !"
                    }
                task_data["assignments"] = assignments
            
            response = await self.client.post("planner/tasks", data=task_data)
            
            return {
                "success": True,
                "task_id": response.get("id"),
                "title": response.get("title"),
                "plan_id": response.get("planId"),
                "bucket_id": response.get("bucketId"),
                "created_date": response.get("createdDateTime"),
                "due_date": response.get("dueDateTime"),
                "priority": response.get("priority")
            }
            
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            raise APIError(f"Failed to create task: {str(e)}")
    
    async def update_task(
        self,
        task_id: str,
        title: Optional[str] = None,
        due_date: Optional[str] = None,
        percent_complete: Optional[int] = None,
        priority: Optional[int] = None
    ) -> Dict[str, Any]:
        """Update task.
        
        Args:
            task_id: Task ID
            title: New title (optional)
            due_date: New due date (optional)
            percent_complete: Completion percentage (optional)
            priority: New priority (optional)
            
        Returns:
            Update confirmation
        """
        try:
            logger.info(f"PlannerTools.update_task - Updating task {task_id}")
            
            # Get current task to get etag
            current_task = await self.client.get(f"planner/tasks/{task_id}")
            etag = current_task.get("@odata.etag")
            
            update_data = {}
            
            if title is not None:
                update_data["title"] = title
            
            if due_date is not None:
                update_data["dueDateTime"] = due_date
            
            if percent_complete is not None:
                update_data["percentComplete"] = percent_complete
            
            if priority is not None:
                update_data["priority"] = priority
            
            if not update_data:
                raise ValidationError("At least one field must be updated")
            
            headers = {
                "If-Match": etag
            }
            
            response = await self.client.patch(
                f"planner/tasks/{task_id}",
                data=update_data,
                headers=headers
            )
            
            return {
                "success": True,
                "message": "Task updated successfully",
                "task_id": task_id,
                "updated_fields": list(update_data.keys())
            }
            
        except Exception as e:
            logger.error(f"Error updating task: {e}")
            raise APIError(f"Failed to update task: {str(e)}")
    
    async def delete_task(self, task_id: str) -> Dict[str, Any]:
        """Delete task.
        
        Args:
            task_id: Task ID
            
        Returns:
            Delete confirmation
        """
        try:
            logger.info(f"PlannerTools.delete_task - Deleting task {task_id}")
            
            # Get current task to get etag
            current_task = await self.client.get(f"planner/tasks/{task_id}")
            etag = current_task.get("@odata.etag")
            
            headers = {
                "If-Match": etag
            }
            
            await self.client.delete(f"planner/tasks/{task_id}", headers=headers)
            
            return {
                "success": True,
                "message": "Task deleted successfully",
                "task_id": task_id
            }
            
        except Exception as e:
            logger.error(f"Error deleting task: {e}")
            raise APIError(f"Failed to delete task: {str(e)}")
