# Office 365 MCP Server Configuration
# Copy this file to .env and fill in your values

# Required: Azure AD Tenant Information
O365_TENANT_ID=your-tenant-id-here
O365_CLIENT_ID=your-client-id-here

# Authentication Method 1: Client Secret (recommended for production)
O365_CLIENT_SECRET=your-client-secret-here

# Authentication Method 2: Certificate (most secure)
# O365_CERTIFICATE_PATH=/path/to/your/certificate.pem
# O365_CERTIFICATE_THUMBPRINT=your-certificate-thumbprint

# Authentication Method 3: User Credentials (development/testing only)
# O365_USERNAME=<EMAIL>
# O365_PASSWORD=your-password

# Optional: SharePoint Configuration
# O365_SHAREPOINT_BASE_URL=https://yourtenant.sharepoint.com

# Optional: Server Settings
O365_SERVER_NAME=Office365-MCP-Server
O365_SERVER_VERSION=1.0.0
O365_REQUEST_TIMEOUT=30
O365_MAX_RETRIES=3
O365_DEFAULT_PAGE_SIZE=100
O365_MAX_PAGE_SIZE=1000

# Optional: Logging Level (DEBUG, INFO, WARNING, ERROR)
# LOG_LEVEL=INFO
