"""Configuration settings for Office 365 MCP Server."""

import os
from typing import Optional
from pydantic import BaseSettings, Field


class Office365Config(BaseSettings):
    """Configuration for Office 365 MCP Server."""
    
    # Microsoft Graph API settings
    graph_base_url: str = Field(
        default="https://graph.microsoft.com/v1.0",
        description="Microsoft Graph API base URL"
    )
    
    # SharePoint settings
    sharepoint_base_url: Optional[str] = Field(
        default=None,
        description="SharePoint site URL (e.g., https://tenant.sharepoint.com)"
    )
    
    # Authentication settings
    tenant_id: Optional[str] = Field(
        default=None,
        description="Azure AD tenant ID"
    )
    
    client_id: Optional[str] = Field(
        default=None,
        description="Azure AD application client ID"
    )
    
    client_secret: Optional[str] = Field(
        default=None,
        description="Azure AD application client secret"
    )
    
    # Certificate authentication
    certificate_path: Optional[str] = Field(
        default=None,
        description="Path to certificate file for certificate authentication"
    )
    
    certificate_thumbprint: Optional[str] = Field(
        default=None,
        description="Certificate thumbprint for certificate authentication"
    )
    
    # User credentials (for development/testing only)
    username: Optional[str] = Field(
        default=None,
        description="Username for user credential authentication"
    )
    
    password: Optional[str] = Field(
        default=None,
        description="Password for user credential authentication"
    )
    
    # Server settings
    server_name: str = Field(
        default="Office365-MCP-Server",
        description="MCP server name"
    )
    
    server_version: str = Field(
        default="1.0.0",
        description="MCP server version"
    )
    
    # Request settings
    request_timeout: int = Field(
        default=30,
        description="HTTP request timeout in seconds"
    )
    
    max_retries: int = Field(
        default=3,
        description="Maximum number of request retries"
    )
    
    # Pagination settings
    default_page_size: int = Field(
        default=100,
        description="Default page size for paginated requests"
    )
    
    max_page_size: int = Field(
        default=1000,
        description="Maximum page size for paginated requests"
    )
    
    class Config:
        env_prefix = "O365_"
        case_sensitive = False


# Global configuration instance
config = Office365Config()


def get_config() -> Office365Config:
    """Get the global configuration instance."""
    return config


def validate_config() -> bool:
    """Validate that required configuration is present."""
    if not config.tenant_id:
        raise ValueError("Tenant ID is required")
    
    if not config.client_id:
        raise ValueError("Client ID is required")
    
    # Check that at least one authentication method is configured
    has_client_secret = bool(config.client_secret)
    has_certificate = bool(config.certificate_path and config.certificate_thumbprint)
    has_user_creds = bool(config.username and config.password)
    
    if not (has_client_secret or has_certificate or has_user_creds):
        raise ValueError(
            "At least one authentication method must be configured: "
            "client_secret, certificate, or user credentials"
        )
    
    return True
