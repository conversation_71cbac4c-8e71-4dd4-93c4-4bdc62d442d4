#!/usr/bin/env python3
"""
Example usage of the Office 365 MCP Server

This script demonstrates how to use the MCP server tools and resources.
"""

import asyncio
import os
import sys
from typing import Any, Dict

# Add the server directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Office365Config, validate_config
from clients import GraphClient, SharePointClient
from tools import OutlookTools, OneDriveTools, TeamsTools
from utils.exceptions import Office365Error


async def test_outlook_operations():
    """Test Outlook operations."""
    print("\n=== Testing Outlook Operations ===")
    
    try:
        config = Office365Config()
        graph_client = GraphClient(config)
        outlook_tools = OutlookTools(graph_client)
        
        # List recent messages
        print("Listing recent messages...")
        messages = await outlook_tools.list_messages(limit=5)
        print(f"Found {messages['count']} messages")
        
        for i, msg in enumerate(messages['messages'][:3], 1):
            print(f"{i}. {msg['subject']} from {msg['from']}")
        
        await graph_client.close()
        
    except Exception as e:
        print(f"Error testing Outlook: {e}")


async def test_onedrive_operations():
    """Test OneDrive operations."""
    print("\n=== Testing OneDrive Operations ===")
    
    try:
        config = Office365Config()
        graph_client = GraphClient(config)
        onedrive_tools = OneDriveTools(graph_client)
        
        # Get drive info
        print("Getting drive information...")
        drive_info = await onedrive_tools.get_drive_info()
        print(f"Drive: {drive_info['name']}")
        print(f"Used: {drive_info['quota']['used_formatted']} / {drive_info['quota']['total_formatted']}")
        
        # List root items
        print("\nListing root folder items...")
        items = await onedrive_tools.list_items()
        print(f"Found {items['count']} items")
        
        for i, item in enumerate(items['items'][:3], 1):
            print(f"{i}. {item['name']} ({item['type']}) - {item['size_formatted']}")
        
        await graph_client.close()
        
    except Exception as e:
        print(f"Error testing OneDrive: {e}")


async def test_teams_operations():
    """Test Teams operations."""
    print("\n=== Testing Teams Operations ===")
    
    try:
        config = Office365Config()
        graph_client = GraphClient(config)
        teams_tools = TeamsTools(graph_client)
        
        # List teams
        print("Listing teams...")
        teams = await teams_tools.list_teams()
        print(f"Found {teams['count']} teams")
        
        for i, team in enumerate(teams['teams'][:3], 1):
            print(f"{i}. {team['display_name']} ({team['visibility']})")
        
        await graph_client.close()
        
    except Exception as e:
        print(f"Error testing Teams: {e}")


async def test_sharepoint_operations():
    """Test SharePoint operations (if configured)."""
    print("\n=== Testing SharePoint Operations ===")
    
    try:
        config = Office365Config()
        
        if not config.sharepoint_base_url:
            print("SharePoint not configured - skipping tests")
            return
        
        sharepoint_client = SharePointClient(config)
        
        # Get site info
        print("Getting site information...")
        site_info = await sharepoint_client.get_web()
        web_data = site_info.get("d", {})
        print(f"Site: {web_data.get('Title', 'Unknown')}")
        print(f"URL: {web_data.get('Url', 'Unknown')}")
        
        await sharepoint_client.close()
        
    except Exception as e:
        print(f"Error testing SharePoint: {e}")


async def main():
    """Main test function."""
    print("Office 365 MCP Server - Example Usage")
    print("=" * 50)
    
    try:
        # Validate configuration
        validate_config()
        print("✓ Configuration validated successfully")
        
        # Test different services
        await test_outlook_operations()
        await test_onedrive_operations()
        await test_teams_operations()
        await test_sharepoint_operations()
        
        print("\n=== All Tests Completed ===")
        
    except Exception as e:
        print(f"Configuration error: {e}")
        print("\nPlease ensure you have set the required environment variables:")
        print("- O365_TENANT_ID")
        print("- O365_CLIENT_ID")
        print("- O365_CLIENT_SECRET (or certificate credentials)")
        return 1
    
    return 0


if __name__ == "__main__":
    # Run the example
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
