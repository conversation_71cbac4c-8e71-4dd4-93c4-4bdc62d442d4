"""Microsoft Graph API client for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

from .base_client import BaseClient
from ..config import Office365Config
from ..utils.helpers import build_query_params

logger = logging.getLogger(__name__)


class GraphClient(BaseClient):
    """Microsoft Graph API client."""
    
    def __init__(self, config: Office365Config):
        """Initialize Graph client.
        
        Args:
            config: Office 365 configuration
        """
        super().__init__(config)
        self.base_url = config.graph_base_url
    
    def _build_url(self, endpoint: str) -> str:
        """Build full URL from endpoint.
        
        Args:
            endpoint: API endpoint
            
        Returns:
            Full URL
        """
        return urljoin(self.base_url + "/", endpoint.lstrip("/"))
    
    # User operations
    async def get_me(self) -> Dict[str, Any]:
        """Get current user information."""
        return await self.get("me")
    
    async def get_user(self, user_id: str) -> Dict[str, Any]:
        """Get user by ID or UPN."""
        return await self.get(f"users/{user_id}")
    
    async def list_users(
        self,
        filter_dict: Optional[Dict[str, Any]] = None,
        select_fields: Optional[List[str]] = None,
        top: Optional[int] = None
    ) -> Dict[str, Any]:
        """List users with optional filtering."""
        params = build_query_params(
            filter_dict=filter_dict,
            select_fields=select_fields,
            top=top
        )
        return await self.get("users", params=params)
    
    # Mail operations
    async def list_messages(
        self,
        user_id: str = "me",
        folder_id: Optional[str] = None,
        filter_dict: Optional[Dict[str, Any]] = None,
        select_fields: Optional[List[str]] = None,
        top: Optional[int] = None
    ) -> Dict[str, Any]:
        """List messages in mailbox."""
        if folder_id:
            endpoint = f"users/{user_id}/mailFolders/{folder_id}/messages"
        else:
            endpoint = f"users/{user_id}/messages"
        
        params = build_query_params(
            filter_dict=filter_dict,
            select_fields=select_fields,
            top=top
        )
        return await self.get(endpoint, params=params)
    
    async def get_message(self, message_id: str, user_id: str = "me") -> Dict[str, Any]:
        """Get specific message."""
        return await self.get(f"users/{user_id}/messages/{message_id}")
    
    async def send_message(
        self,
        to_recipients: List[str],
        subject: str,
        body: str,
        cc_recipients: Optional[List[str]] = None,
        bcc_recipients: Optional[List[str]] = None,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Send email message."""
        message_data = {
            "message": {
                "subject": subject,
                "body": {
                    "contentType": "HTML",
                    "content": body
                },
                "toRecipients": [
                    {"emailAddress": {"address": email}} for email in to_recipients
                ]
            }
        }
        
        if cc_recipients:
            message_data["message"]["ccRecipients"] = [
                {"emailAddress": {"address": email}} for email in cc_recipients
            ]
        
        if bcc_recipients:
            message_data["message"]["bccRecipients"] = [
                {"emailAddress": {"address": email}} for email in bcc_recipients
            ]
        
        return await self.post(f"users/{user_id}/sendMail", data=message_data)
    
    # Calendar operations
    async def list_events(
        self,
        user_id: str = "me",
        calendar_id: Optional[str] = None,
        filter_dict: Optional[Dict[str, Any]] = None,
        select_fields: Optional[List[str]] = None,
        top: Optional[int] = None
    ) -> Dict[str, Any]:
        """List calendar events."""
        if calendar_id:
            endpoint = f"users/{user_id}/calendars/{calendar_id}/events"
        else:
            endpoint = f"users/{user_id}/events"
        
        params = build_query_params(
            filter_dict=filter_dict,
            select_fields=select_fields,
            top=top
        )
        return await self.get(endpoint, params=params)
    
    async def create_event(
        self,
        subject: str,
        start_time: str,
        end_time: str,
        attendees: Optional[List[str]] = None,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Create calendar event."""
        event_data = {
            "subject": subject,
            "start": {
                "dateTime": start_time,
                "timeZone": "UTC"
            },
            "end": {
                "dateTime": end_time,
                "timeZone": "UTC"
            }
        }
        
        if attendees:
            event_data["attendees"] = [
                {
                    "emailAddress": {"address": email, "name": email},
                    "type": "required"
                }
                for email in attendees
            ]
        
        return await self.post(f"users/{user_id}/events", data=event_data)
    
    # OneDrive operations
    async def get_drive(self, user_id: str = "me") -> Dict[str, Any]:
        """Get user's OneDrive."""
        return await self.get(f"users/{user_id}/drive")
    
    async def list_drive_items(
        self,
        item_id: str = "root",
        user_id: str = "me",
        select_fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """List items in OneDrive folder."""
        params = build_query_params(select_fields=select_fields)
        return await self.get(f"users/{user_id}/drive/items/{item_id}/children", params=params)
    
    async def get_drive_item(self, item_id: str, user_id: str = "me") -> Dict[str, Any]:
        """Get OneDrive item."""
        return await self.get(f"users/{user_id}/drive/items/{item_id}")
    
    # Teams operations
    async def list_teams(self) -> Dict[str, Any]:
        """List teams the user is a member of."""
        return await self.get("me/joinedTeams")
    
    async def get_team(self, team_id: str) -> Dict[str, Any]:
        """Get team information."""
        return await self.get(f"teams/{team_id}")
    
    async def list_channels(self, team_id: str) -> Dict[str, Any]:
        """List channels in a team."""
        return await self.get(f"teams/{team_id}/channels")
    
    # Contacts operations
    async def list_contacts(
        self,
        user_id: str = "me",
        folder_id: Optional[str] = None,
        filter_dict: Optional[Dict[str, Any]] = None,
        select_fields: Optional[List[str]] = None,
        top: Optional[int] = None
    ) -> Dict[str, Any]:
        """List contacts."""
        if folder_id:
            endpoint = f"users/{user_id}/contactFolders/{folder_id}/contacts"
        else:
            endpoint = f"users/{user_id}/contacts"
        
        params = build_query_params(
            filter_dict=filter_dict,
            select_fields=select_fields,
            top=top
        )
        return await self.get(endpoint, params=params)
    
    async def create_contact(
        self,
        given_name: str,
        surname: str,
        email_address: Optional[str] = None,
        user_id: str = "me"
    ) -> Dict[str, Any]:
        """Create contact."""
        contact_data = {
            "givenName": given_name,
            "surname": surname
        }
        
        if email_address:
            contact_data["emailAddresses"] = [
                {
                    "address": email_address,
                    "name": f"{given_name} {surname}"
                }
            ]
        
        return await self.post(f"users/{user_id}/contacts", data=contact_data)
