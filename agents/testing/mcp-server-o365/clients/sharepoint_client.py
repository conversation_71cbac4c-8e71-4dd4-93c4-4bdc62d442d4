"""SharePoint API client for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

from .base_client import BaseClient
from ..config import Office365Config
from ..utils.helpers import build_query_params

logger = logging.getLogger(__name__)


class SharePointClient(BaseClient):
    """SharePoint API client."""
    
    def __init__(self, config: Office365Config, site_url: Optional[str] = None):
        """Initialize SharePoint client.
        
        Args:
            config: Office 365 configuration
            site_url: SharePoint site URL
        """
        super().__init__(config)
        self.site_url = site_url or config.sharepoint_base_url
        if not self.site_url:
            raise ValueError("SharePoint site URL is required")
        
        # Ensure site URL ends with /
        if not self.site_url.endswith('/'):
            self.site_url += '/'
        
        self.base_url = urljoin(self.site_url, "_api/")
    
    def _build_url(self, endpoint: str) -> str:
        """Build full URL from endpoint.
        
        Args:
            endpoint: API endpoint
            
        Returns:
            Full URL
        """
        return urljoin(self.base_url, endpoint.lstrip("/"))
    
    async def _get_auth_headers(self, scopes: Optional[List[str]] = None) -> Dict[str, str]:
        """Get authentication headers for SharePoint.
        
        Args:
            scopes: Optional list of scopes for token acquisition
            
        Returns:
            Dictionary of headers including authorization
        """
        try:
            # Use SharePoint-specific token
            access_token = await self.auth_provider.get_sharepoint_token(self.site_url)
            return {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json;odata=verbose",
                "Content-Type": "application/json;odata=verbose"
            }
        except Exception as e:
            logger.error(f"Failed to get SharePoint auth headers: {e}")
            raise AuthenticationError(f"SharePoint authentication failed: {str(e)}")
    
    # Web operations
    async def get_web(self) -> Dict[str, Any]:
        """Get web information."""
        return await self.get("web")
    
    async def get_web_properties(self) -> Dict[str, Any]:
        """Get web properties."""
        return await self.get("web/AllProperties")
    
    # List operations
    async def get_lists(self) -> Dict[str, Any]:
        """Get all lists in the web."""
        return await self.get("web/lists")
    
    async def get_list(self, list_id: str) -> Dict[str, Any]:
        """Get list by ID or title."""
        if len(list_id) == 36 and '-' in list_id:  # GUID format
            return await self.get(f"web/lists(guid'{list_id}')")
        else:
            return await self.get(f"web/lists/getbytitle('{list_id}')")
    
    async def get_list_items(
        self,
        list_id: str,
        filter_dict: Optional[Dict[str, Any]] = None,
        select_fields: Optional[List[str]] = None,
        top: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get items from a list."""
        if len(list_id) == 36 and '-' in list_id:  # GUID format
            endpoint = f"web/lists(guid'{list_id}')/items"
        else:
            endpoint = f"web/lists/getbytitle('{list_id}')/items"
        
        params = build_query_params(
            filter_dict=filter_dict,
            select_fields=select_fields,
            top=top
        )
        return await self.get(endpoint, params=params)
    
    async def create_list_item(
        self,
        list_id: str,
        item_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create item in a list."""
        if len(list_id) == 36 and '-' in list_id:  # GUID format
            endpoint = f"web/lists(guid'{list_id}')/items"
        else:
            endpoint = f"web/lists/getbytitle('{list_id}')/items"
        
        return await self.post(endpoint, data=item_data)
    
    async def update_list_item(
        self,
        list_id: str,
        item_id: int,
        item_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update item in a list."""
        if len(list_id) == 36 and '-' in list_id:  # GUID format
            endpoint = f"web/lists(guid'{list_id}')/items({item_id})"
        else:
            endpoint = f"web/lists/getbytitle('{list_id}')/items({item_id})"
        
        # Add required headers for update
        headers = {
            "X-HTTP-Method": "MERGE",
            "If-Match": "*"
        }
        
        return await self.post(endpoint, data=item_data, headers=headers)
    
    async def delete_list_item(
        self,
        list_id: str,
        item_id: int
    ) -> Dict[str, Any]:
        """Delete item from a list."""
        if len(list_id) == 36 and '-' in list_id:  # GUID format
            endpoint = f"web/lists(guid'{list_id}')/items({item_id})"
        else:
            endpoint = f"web/lists/getbytitle('{list_id}')/items({item_id})"
        
        # Add required headers for delete
        headers = {
            "X-HTTP-Method": "DELETE",
            "If-Match": "*"
        }
        
        return await self.post(endpoint, headers=headers)
    
    # File operations
    async def get_files(self, folder_path: str = "") -> Dict[str, Any]:
        """Get files in a folder."""
        if folder_path:
            endpoint = f"web/getFolderByServerRelativeUrl('{folder_path}')/files"
        else:
            endpoint = "web/files"
        
        return await self.get(endpoint)
    
    async def get_file(self, file_path: str) -> Dict[str, Any]:
        """Get file information."""
        return await self.get(f"web/getFileByServerRelativeUrl('{file_path}')")
    
    async def get_file_content(self, file_path: str) -> bytes:
        """Get file content."""
        endpoint = f"web/getFileByServerRelativeUrl('{file_path}')/$value"
        response = await self._make_request("GET", self._build_url(endpoint))
        
        # Handle binary content
        if isinstance(response, dict) and "content" in response:
            return response["content"].encode()
        return b""
    
    async def upload_file(
        self,
        folder_path: str,
        filename: str,
        content: bytes
    ) -> Dict[str, Any]:
        """Upload file to SharePoint."""
        endpoint = f"web/getFolderByServerRelativeUrl('{folder_path}')/files/add(url='{filename}',overwrite=true)"
        
        # Special handling for file upload
        headers = {
            "Content-Type": "application/octet-stream"
        }
        
        # Note: This is a simplified implementation
        # In practice, you might need to handle large files differently
        return await self.post(endpoint, data=content, headers=headers)
    
    # Folder operations
    async def get_folders(self, folder_path: str = "") -> Dict[str, Any]:
        """Get folders in a directory."""
        if folder_path:
            endpoint = f"web/getFolderByServerRelativeUrl('{folder_path}')/folders"
        else:
            endpoint = "web/folders"
        
        return await self.get(endpoint)
    
    async def create_folder(self, parent_path: str, folder_name: str) -> Dict[str, Any]:
        """Create folder in SharePoint."""
        endpoint = f"web/folders"
        folder_data = {
            "ServerRelativeUrl": f"{parent_path}/{folder_name}"
        }
        
        return await self.post(endpoint, data=folder_data)
    
    # User and group operations
    async def get_site_users(self) -> Dict[str, Any]:
        """Get site users."""
        return await self.get("web/siteusers")
    
    async def get_site_groups(self) -> Dict[str, Any]:
        """Get site groups."""
        return await self.get("web/sitegroups")
    
    async def get_current_user(self) -> Dict[str, Any]:
        """Get current user information."""
        return await self.get("web/currentuser")
