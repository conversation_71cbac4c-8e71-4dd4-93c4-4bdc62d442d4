"""Base client for Office 365 API interactions."""

import logging
from typing import Any, Dict, List, Optional, Union
from abc import ABC, abstractmethod

import httpx

from ..config import Office365Config
from ..utils.exceptions import APIError, AuthenticationError
from ..auth import ClientCredentialsAuth, UserCredentialsAuth, CertificateAuth

logger = logging.getLogger(__name__)


class BaseClient(ABC):
    """Base client for Office 365 API interactions."""
    
    def __init__(self, config: Office365Config):
        """Initialize base client.
        
        Args:
            config: Office 365 configuration
        """
        self.config = config
        self.auth_provider = self._create_auth_provider()
        self._http_client: Optional[httpx.AsyncClient] = None
    
    def _create_auth_provider(self) -> Union[ClientCredentialsAuth, UserCredentialsAuth, CertificateAuth]:
        """Create appropriate authentication provider based on configuration."""
        if self.config.client_secret:
            return ClientCredentialsAuth(self.config)
        elif self.config.certificate_path and self.config.certificate_thumbprint:
            return CertificateAuth(self.config)
        elif self.config.username and self.config.password:
            return UserCredentialsAuth(self.config)
        else:
            raise AuthenticationError("No valid authentication method configured")
    
    async def _get_http_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self._http_client is None:
            self._http_client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.config.request_timeout),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
        return self._http_client
    
    async def _get_auth_headers(self, scopes: Optional[List[str]] = None) -> Dict[str, str]:
        """Get authentication headers.
        
        Args:
            scopes: Optional list of scopes for token acquisition
            
        Returns:
            Dictionary of headers including authorization
        """
        try:
            access_token = await self.auth_provider.get_access_token(scopes)
            return {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
        except Exception as e:
            logger.error(f"Failed to get auth headers: {e}")
            raise AuthenticationError(f"Authentication failed: {str(e)}")
    
    async def _make_request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        scopes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Make HTTP request to API.
        
        Args:
            method: HTTP method
            url: Request URL
            params: Query parameters
            data: Request body data
            headers: Additional headers
            scopes: OAuth scopes for authentication
            
        Returns:
            Response data as dictionary
            
        Raises:
            APIError: If request fails
        """
        client = await self._get_http_client()
        auth_headers = await self._get_auth_headers(scopes)
        
        # Merge headers
        request_headers = {**auth_headers}
        if headers:
            request_headers.update(headers)
        
        try:
            logger.debug(f"Making {method} request to {url}")
            
            response = await client.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=request_headers
            )
            
            logger.debug(f"Response status: {response.status_code}")
            
            # Handle different response status codes
            if response.status_code == 204:
                # No content response
                return {"success": True}
            elif response.status_code >= 400:
                # Error response
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    pass
                
                error_msg = f"API request failed with status {response.status_code}"
                if error_data.get("error", {}).get("message"):
                    error_msg += f": {error_data['error']['message']}"
                
                raise APIError(
                    message=error_msg,
                    status_code=response.status_code,
                    response_data=error_data
                )
            
            # Success response
            try:
                return response.json()
            except:
                # Handle non-JSON responses
                return {"content": response.text, "success": True}
                
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise APIError(f"Request failed: {str(e)}")
        except APIError:
            # Re-raise API errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            raise APIError(f"Unexpected error: {str(e)}")
    
    async def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        scopes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Make GET request.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            headers: Additional headers
            scopes: OAuth scopes
            
        Returns:
            Response data
        """
        url = self._build_url(endpoint)
        return await self._make_request("GET", url, params=params, headers=headers, scopes=scopes)
    
    async def post(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        scopes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Make POST request.
        
        Args:
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            headers: Additional headers
            scopes: OAuth scopes
            
        Returns:
            Response data
        """
        url = self._build_url(endpoint)
        return await self._make_request("POST", url, params=params, data=data, headers=headers, scopes=scopes)
    
    async def patch(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        scopes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Make PATCH request.
        
        Args:
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            headers: Additional headers
            scopes: OAuth scopes
            
        Returns:
            Response data
        """
        url = self._build_url(endpoint)
        return await self._make_request("PATCH", url, params=params, data=data, headers=headers, scopes=scopes)
    
    async def delete(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        scopes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Make DELETE request.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            headers: Additional headers
            scopes: OAuth scopes
            
        Returns:
            Response data
        """
        url = self._build_url(endpoint)
        return await self._make_request("DELETE", url, params=params, headers=headers, scopes=scopes)
    
    @abstractmethod
    def _build_url(self, endpoint: str) -> str:
        """Build full URL from endpoint.
        
        Args:
            endpoint: API endpoint
            
        Returns:
            Full URL
        """
        pass
    
    async def close(self) -> None:
        """Close HTTP client."""
        if self._http_client:
            await self._http_client.aclose()
            self._http_client = None
