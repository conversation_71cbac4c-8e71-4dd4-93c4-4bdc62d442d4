# Office 365 MCP Server

A modern Model Context Protocol (MCP) server providing comprehensive Office 365 functionality including Outlook, SharePoint, OneDrive, Teams, OneNote, and Planner operations.

This server is based on the `office365-rest-python-client` library but rewritten using the modern MCP Python SDK for better integration with LLMs and other applications.

## Features

### Supported Services

- **Outlook**: Email management, calendar operations, contacts
- **OneDrive**: File and folder operations, search, sharing
- **SharePoint**: Site management, lists, document libraries
- **Teams**: Team and channel management, messaging
- **OneNote**: Notebook, section, and page operations
- **Planner**: Task and project management

### Authentication Methods

- **Client Credentials**: App-only authentication using client ID and secret
- **Certificate Authentication**: Certificate-based authentication for enhanced security
- **User Credentials**: Username/password authentication (for development/testing)

### MCP Features

- **Tools**: 30+ tools for Office 365 operations
- **Resources**: Rich data exposure through MCP resources
- **Error Handling**: Comprehensive error handling and logging
- **Type Safety**: Full type annotations and validation

## Installation

1. **Clone or create the project directory:**
   ```bash
   mkdir -p /Users/<USER>/git/agents/testing/mcp-server-o365
   cd /Users/<USER>/git/agents/testing/mcp-server-o365
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Configuration

### Environment Variables

Set the following environment variables (prefix with `O365_`):

```bash
# Required
export O365_TENANT_ID="your-tenant-id"
export O365_CLIENT_ID="your-client-id"

# Authentication method 1: Client Secret
export O365_CLIENT_SECRET="your-client-secret"

# Authentication method 2: Certificate
export O365_CERTIFICATE_PATH="/path/to/certificate.pem"
export O365_CERTIFICATE_THUMBPRINT="certificate-thumbprint"

# Authentication method 3: User Credentials (dev/test only)
export O365_USERNAME="<EMAIL>"
export O365_PASSWORD="password"

# Optional: SharePoint
export O365_SHAREPOINT_BASE_URL="https://tenant.sharepoint.com"

# Optional: Server settings
export O365_SERVER_NAME="Office365-MCP-Server"
export O365_REQUEST_TIMEOUT="30"
export O365_DEFAULT_PAGE_SIZE="100"
```

### Azure AD App Registration

1. **Register an application** in Azure AD
2. **Configure API permissions** for Microsoft Graph:
   - `Mail.Read`, `Mail.Send`, `Mail.ReadWrite`
   - `Calendars.Read`, `Calendars.ReadWrite`
   - `Files.Read`, `Files.ReadWrite`, `Files.ReadWrite.All`
   - `Sites.Read.All`, `Sites.ReadWrite.All`
   - `Team.ReadBasic.All`, `Channel.ReadBasic.All`
   - `Notes.Read`, `Notes.ReadWrite`
   - `Tasks.Read`, `Tasks.ReadWrite`
   - `User.Read`, `User.ReadBasic.All`

3. **For SharePoint**, add permissions:
   - `Sites.FullControl.All` or specific site permissions

4. **Grant admin consent** for the permissions

## Usage

### Starting the Server

```bash
python server.py
```

### Using with MCP Clients

The server exposes tools and resources that can be used by MCP-compatible clients:

#### Tools Examples

```python
# List recent emails
await client.call_tool("list_messages", {
    "limit": 10,
    "unread_only": True
})

# Send an email
await client.call_tool("send_message", {
    "to_recipients": ["<EMAIL>"],
    "subject": "Hello from MCP",
    "body": "This email was sent via the Office 365 MCP Server!"
})

# List OneDrive files
await client.call_tool("list_drive_items", {
    "folder_id": "root",
    "item_type": "file"
})

# Create a Teams message
await client.call_tool("send_channel_message", {
    "team_id": "team-id",
    "channel_id": "channel-id",
    "message": "Hello from MCP!"
})
```

#### Resources Examples

```python
# Get user profile
profile = await client.read_resource("outlook://profile/me")

# Get mailbox summary
mailbox = await client.read_resource("outlook://mailbox/me")

# Get OneDrive overview
drive = await client.read_resource("onedrive://overview/me")

# Get Teams overview
teams = await client.read_resource("teams://overview")
```

## Available Tools

### Outlook Tools
- `list_messages` - List email messages with filtering
- `get_message` - Get specific message details
- `send_message` - Send email messages
- `mark_message_as_read` - Mark messages as read
- `delete_message` - Delete messages

### OneDrive Tools
- `get_drive_info` - Get drive information and quota
- `list_drive_items` - List files and folders
- `get_drive_item` - Get specific item details
- `create_folder` - Create new folders
- `delete_drive_item` - Delete files/folders
- `search_drive_items` - Search for items

### Teams Tools
- `list_teams` - List user's teams
- `get_team` - Get team details
- `list_channels` - List team channels
- `get_channel_messages` - Get channel messages
- `send_channel_message` - Send messages to channels
- `list_team_members` - List team members

### SharePoint Tools (if configured)
- `get_sharepoint_site_info` - Get site information
- `list_sharepoint_lists` - List all lists
- `get_sharepoint_list_items` - Get list items
- `create_sharepoint_list_item` - Create list items
- `list_sharepoint_files` - List files in folders

### OneNote Tools
- `list_notebooks` - List notebooks
- `list_onenote_pages` - List pages
- `create_onenote_page` - Create new pages
- `search_onenote_pages` - Search pages

### Planner Tools
- `list_planner_plans` - List plans
- `list_planner_tasks` - List tasks
- `create_planner_task` - Create new tasks
- `update_planner_task` - Update existing tasks

## Available Resources

### Outlook Resources
- `outlook://profile/{user_id}` - User profile information
- `outlook://mailbox/{user_id}` - Mailbox summary
- `outlook://calendar/{user_id}` - Calendar summary
- `outlook://message/{message_id}` - Message content

### OneDrive Resources
- `onedrive://overview/{user_id}` - Drive overview and quota
- `onedrive://folder/{folder_id}` - Folder contents

### Teams Resources
- `teams://overview` - Teams overview
- `teams://team/{team_id}` - Team details

### SharePoint Resources (if configured)
- `sharepoint://site` - Site overview
- `sharepoint://list/{list_name}` - List schema

## Error Handling

The server includes comprehensive error handling:

- **Authentication errors**: Clear messages for auth failures
- **API errors**: Detailed error responses with status codes
- **Validation errors**: Input validation with helpful messages
- **Configuration errors**: Startup validation of required settings

## Logging

The server uses structured logging with different levels:

- **INFO**: General operation information
- **DEBUG**: Detailed debugging information
- **ERROR**: Error conditions and exceptions
- **WARNING**: Warning conditions

## Security Considerations

1. **Use certificate authentication** in production environments
2. **Limit API permissions** to only what's needed
3. **Secure credential storage** - never commit secrets to code
4. **Regular token rotation** for enhanced security
5. **Monitor API usage** and implement rate limiting if needed

## Comparison with office365-rest-python-client

This MCP server provides the same functionality as the original library but with:

- **Modern MCP protocol** support for LLM integration
- **Async/await** patterns throughout
- **Type safety** with full annotations
- **Better error handling** and logging
- **Resource exposure** for data access
- **Standardized API** through MCP tools

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:

1. Check the logs for detailed error messages
2. Verify Azure AD app permissions
3. Ensure all required environment variables are set
4. Test authentication with a simple tool call

## Changelog

### Version 1.0.0
- Initial release with full Office 365 API coverage
- Support for all major Office 365 services
- Comprehensive MCP tools and resources
- Multiple authentication methods
- Full type safety and error handling
