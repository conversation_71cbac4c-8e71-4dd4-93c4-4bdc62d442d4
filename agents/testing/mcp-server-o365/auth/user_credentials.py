"""User credentials authentication for Office 365 MCP Server."""

import logging
from typing import Dict, Optional

import msal

from ..config import Office365Config
from ..utils.exceptions import AuthenticationError

logger = logging.getLogger(__name__)


class UserCredentialsAuth:
    """User credentials authentication using MSAL."""
    
    def __init__(self, config: Office365Config):
        """Initialize user credentials authentication.
        
        Args:
            config: Office 365 configuration
        """
        self.config = config
        self.app: Optional[msal.PublicClientApplication] = None
        self._access_token: Optional[str] = None
        
        if not config.tenant_id:
            raise AuthenticationError("Tenant ID is required for user credentials auth")
        
        if not config.client_id:
            raise AuthenticationError("Client ID is required for user credentials auth")
        
        if not config.username:
            raise AuthenticationError("Username is required for user credentials auth")
        
        if not config.password:
            raise AuthenticationError("Password is required for user credentials auth")
        
        self._initialize_app()
    
    def _initialize_app(self) -> None:
        """Initialize MSAL public client application."""
        try:
            authority = f"https://login.microsoftonline.com/{self.config.tenant_id}"
            
            self.app = msal.PublicClientApplication(
                client_id=self.config.client_id,
                authority=authority
            )
            
            logger.info("MSAL public client application initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize MSAL app: {e}")
            raise AuthenticationError(f"Failed to initialize authentication: {str(e)}")
    
    async def get_access_token(self, scopes: Optional[list] = None) -> str:
        """Get access token using username and password.
        
        Args:
            scopes: List of scopes to request. Defaults to Graph API scope.
            
        Returns:
            Access token string
            
        Raises:
            AuthenticationError: If token acquisition fails
        """
        if not self.app:
            raise AuthenticationError("MSAL app not initialized")
        
        if scopes is None:
            scopes = ["https://graph.microsoft.com/.default"]
        
        try:
            # Try to get token from cache first
            accounts = self.app.get_accounts(username=self.config.username)
            if accounts:
                result = self.app.acquire_token_silent(scopes, account=accounts[0])
                if result and "access_token" in result:
                    self._access_token = result["access_token"]
                    logger.debug("Access token acquired from cache")
                    return self._access_token
            
            # Acquire new token using username and password
            result = self.app.acquire_token_by_username_password(
                username=self.config.username,
                password=self.config.password,
                scopes=scopes
            )
            
            if "access_token" in result:
                self._access_token = result["access_token"]
                logger.info("Access token acquired successfully")
                return self._access_token
            else:
                error_msg = result.get("error_description", "Unknown error")
                logger.error(f"Token acquisition failed: {error_msg}")
                raise AuthenticationError(f"Failed to acquire access token: {error_msg}")
                
        except Exception as e:
            logger.error(f"Error acquiring access token: {e}")
            raise AuthenticationError(f"Authentication failed: {str(e)}")
    
    async def get_sharepoint_token(self, site_url: str) -> str:
        """Get access token for SharePoint site.
        
        Args:
            site_url: SharePoint site URL
            
        Returns:
            Access token string
        """
        # Extract tenant from site URL for SharePoint-specific scope
        if "sharepoint.com" in site_url:
            # For SharePoint Online, use the site-specific scope
            from urllib.parse import urlparse
            parsed = urlparse(site_url)
            sharepoint_scope = f"https://{parsed.netloc}/.default"
            return await self.get_access_token([sharepoint_scope])
        else:
            # For on-premises SharePoint, use the site URL as scope
            return await self.get_access_token([f"{site_url}/.default"])
    
    def get_current_token(self) -> Optional[str]:
        """Get the current access token without refreshing.
        
        Returns:
            Current access token or None if not available
        """
        return self._access_token
    
    def clear_token(self) -> None:
        """Clear the current access token."""
        self._access_token = None
        logger.debug("Access token cleared")
