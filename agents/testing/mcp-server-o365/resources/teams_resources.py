"""Teams resources for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.graph_client import GraphClient
from ..utils.exceptions import APIError

logger = logging.getLogger(__name__)


class TeamsResources:
    """Resources for Microsoft Teams data exposure."""
    
    def __init__(self, graph_client: GraphClient):
        """Initialize Teams resources.
        
        Args:
            graph_client: Microsoft Graph client
        """
        self.client = graph_client
    
    async def get_teams_overview(self) -> str:
        """Get Teams overview as a resource.
        
        Returns:
            Teams overview as formatted text
        """
        try:
            logger.info("TeamsResources.get_teams_overview - Getting Teams overview")
            
            teams_info = await self.client.list_teams()
            teams_data = teams_info.get("value", [])
            
            overview_text = f"""
Microsoft Teams Overview:
========================

Total Teams: {len(teams_data)}

Your Teams:
"""
            
            for i, team in enumerate(teams_data, 1):
                name = team.get("displayName", "Untitled Team")
                description = team.get("description", "No description")
                visibility = team.get("visibility", "Unknown")
                created = team.get("createdDateTime", "Unknown")
                archived = team.get("isArchived", False)
                
                overview_text += f"""
{i}. {name}
   - Description: {description[:100]}{'...' if len(description) > 100 else ''}
   - Visibility: {visibility}
   - Created: {created}
   - Status: {'Archived' if archived else 'Active'}
   - Web URL: {team.get('webUrl', 'N/A')}
"""
            
            return overview_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting Teams overview: {e}")
            raise APIError(f"Failed to get Teams overview: {str(e)}")
    
    async def get_team_details(self, team_id: str) -> str:
        """Get team details as a resource.
        
        Args:
            team_id: Team ID
            
        Returns:
            Team details as formatted text
        """
        try:
            logger.info(f"TeamsResources.get_team_details - Getting details for team {team_id}")
            
            team_info = await self.client.get_team(team_id)
            channels_info = await self.client.list_channels(team_id)
            
            channels_data = channels_info.get("value", [])
            
            details_text = f"""
Team Details:
============

Basic Information:
- Name: {team_info.get('displayName', 'N/A')}
- Description: {team_info.get('description', 'No description')}
- Visibility: {team_info.get('visibility', 'Unknown')}
- Created: {team_info.get('createdDateTime', 'Unknown')}
- Status: {'Archived' if team_info.get('isArchived', False) else 'Active'}
- Web URL: {team_info.get('webUrl', 'N/A')}

Settings:
- Member Settings: {team_info.get('memberSettings', {})}
- Guest Settings: {team_info.get('guestSettings', {})}
- Messaging Settings: {team_info.get('messagingSettings', {})}
- Fun Settings: {team_info.get('funSettings', {})}

Channels ({len(channels_data)}):
"""
            
            for i, channel in enumerate(channels_data, 1):
                channel_name = channel.get("displayName", "Untitled Channel")
                channel_desc = channel.get("description", "No description")
                membership_type = channel.get("membershipType", "Unknown")
                
                details_text += f"""
{i}. {channel_name}
   - Description: {channel_desc[:80]}{'...' if len(channel_desc) > 80 else ''}
   - Type: {membership_type}
   - Email: {channel.get('email', 'N/A')}
   - Web URL: {channel.get('webUrl', 'N/A')}
"""
            
            return details_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting team details: {e}")
            raise APIError(f"Failed to get team details: {str(e)}")
    
    async def get_channel_activity(self, team_id: str, channel_id: str, limit: int = 10) -> str:
        """Get channel activity as a resource.
        
        Args:
            team_id: Team ID
            channel_id: Channel ID
            limit: Number of messages to include
            
        Returns:
            Channel activity as formatted text
        """
        try:
            logger.info(f"TeamsResources.get_channel_activity - Getting activity for channel {channel_id}")
            
            # Get channel info
            channels_info = await self.client.list_channels(team_id)
            channels_data = channels_info.get("value", [])
            
            channel_info = None
            for channel in channels_data:
                if channel.get("id") == channel_id:
                    channel_info = channel
                    break
            
            if not channel_info:
                raise APIError(f"Channel {channel_id} not found")
            
            # Get recent messages
            params = {"$top": limit, "$orderby": "createdDateTime desc"}
            
            messages_info = await self.client.get(
                f"teams/{team_id}/channels/{channel_id}/messages",
                params=params
            )
            
            messages_data = messages_info.get("value", [])
            
            activity_text = f"""
Channel Activity:
================

Channel: {channel_info.get('displayName', 'Unknown')}
Team ID: {team_id}
Channel ID: {channel_id}

Recent Messages ({len(messages_data)}):
"""
            
            for i, message in enumerate(messages_data, 1):
                created = message.get("createdDateTime", "Unknown")
                from_user = message.get("from", {}).get("user", {}).get("displayName", "Unknown")
                message_type = message.get("messageType", "message")
                subject = message.get("subject", "")
                
                body = message.get("body", {})
                content = body.get("content", "") if body else ""
                
                # Clean up HTML content for preview
                import re
                content_preview = re.sub(r'<[^>]+>', '', content)[:100]
                if len(content_preview) == 100:
                    content_preview += "..."
                
                activity_text += f"""
{i}. [{created}] {from_user}
   - Type: {message_type}
   - Subject: {subject}
   - Preview: {content_preview}
   - Replies: {len(message.get('replies', []))}
"""
            
            return activity_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting channel activity: {e}")
            raise APIError(f"Failed to get channel activity: {str(e)}")
    
    async def get_team_members(self, team_id: str) -> str:
        """Get team members as a resource.
        
        Args:
            team_id: Team ID
            
        Returns:
            Team members list as formatted text
        """
        try:
            logger.info(f"TeamsResources.get_team_members - Getting members for team {team_id}")
            
            members_info = await self.client.get(f"teams/{team_id}/members")
            members_data = members_info.get("value", [])
            
            members_text = f"""
Team Members:
============

Team ID: {team_id}
Total Members: {len(members_data)}

Members List:
"""
            
            for i, member in enumerate(members_data, 1):
                name = member.get("displayName", "Unknown")
                email = member.get("email", "N/A")
                roles = member.get("roles", [])
                user_id = member.get("userId", "N/A")
                
                role_str = ", ".join(roles) if roles else "Member"
                
                members_text += f"""
{i}. {name}
   - Email: {email}
   - Role: {role_str}
   - User ID: {user_id}
"""
            
            return members_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting team members: {e}")
            raise APIError(f"Failed to get team members: {str(e)}")
    
    async def get_team_apps(self, team_id: str) -> str:
        """Get team apps as a resource.
        
        Args:
            team_id: Team ID
            
        Returns:
            Team apps list as formatted text
        """
        try:
            logger.info(f"TeamsResources.get_team_apps - Getting apps for team {team_id}")
            
            apps_info = await self.client.get(f"teams/{team_id}/installedApps")
            apps_data = apps_info.get("value", [])
            
            apps_text = f"""
Team Apps:
=========

Team ID: {team_id}
Total Installed Apps: {len(apps_data)}

Installed Apps:
"""
            
            for i, app in enumerate(apps_data, 1):
                teams_app = app.get("teamsApp", {})
                app_name = teams_app.get("displayName", "Unknown App")
                app_id = teams_app.get("id", "N/A")
                distribution_method = teams_app.get("distributionMethod", "Unknown")
                external_id = teams_app.get("externalId", "N/A")
                
                apps_text += f"""
{i}. {app_name}
   - App ID: {app_id}
   - Distribution: {distribution_method}
   - External ID: {external_id}
"""
            
            return apps_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting team apps: {e}")
            raise APIError(f"Failed to get team apps: {str(e)}")
