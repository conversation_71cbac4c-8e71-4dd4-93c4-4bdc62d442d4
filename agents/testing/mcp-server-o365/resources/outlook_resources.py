"""Outlook resources for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.graph_client import GraphClient
from ..utils.exceptions import APIError

logger = logging.getLogger(__name__)


class OutlookResources:
    """Resources for Outlook data exposure."""
    
    def __init__(self, graph_client: GraphClient):
        """Initialize Outlook resources.
        
        Args:
            graph_client: Microsoft Graph client
        """
        self.client = graph_client
    
    async def get_user_profile(self, user_id: str = "me") -> str:
        """Get user profile information as a resource.
        
        Args:
            user_id: User ID or 'me' for current user
            
        Returns:
            User profile information as formatted text
        """
        try:
            logger.info(f"OutlookResources.get_user_profile - Getting profile for user {user_id}")
            
            if user_id == "me":
                response = await self.client.get_me()
            else:
                response = await self.client.get_user(user_id)
            
            profile_text = f"""
User Profile Information:
========================

Display Name: {response.get('displayName', 'N/A')}
Email: {response.get('mail', response.get('userPrincipalName', 'N/A'))}
Job Title: {response.get('jobTitle', 'N/A')}
Department: {response.get('department', 'N/A')}
Office Location: {response.get('officeLocation', 'N/A')}
Mobile Phone: {response.get('mobilePhone', 'N/A')}
Business Phones: {', '.join(response.get('businessPhones', []))}
Preferred Language: {response.get('preferredLanguage', 'N/A')}
User ID: {response.get('id', 'N/A')}
User Principal Name: {response.get('userPrincipalName', 'N/A')}
"""
            
            return profile_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting user profile: {e}")
            raise APIError(f"Failed to get user profile: {str(e)}")
    
    async def get_mailbox_summary(self, user_id: str = "me") -> str:
        """Get mailbox summary as a resource.
        
        Args:
            user_id: User ID or 'me' for current user
            
        Returns:
            Mailbox summary as formatted text
        """
        try:
            logger.info(f"OutlookResources.get_mailbox_summary - Getting mailbox summary for user {user_id}")
            
            # Get recent messages
            recent_messages = await self.client.list_messages(
                user_id=user_id,
                select_fields=["subject", "from", "receivedDateTime", "isRead"],
                top=10
            )
            
            # Get unread count
            unread_messages = await self.client.list_messages(
                user_id=user_id,
                filter_dict={"isRead": False},
                select_fields=["id"],
                top=100
            )
            
            messages = recent_messages.get("value", [])
            unread_count = len(unread_messages.get("value", []))
            
            summary_text = f"""
Mailbox Summary:
===============

Total Recent Messages: {len(messages)}
Unread Messages: {unread_count}

Recent Messages:
"""
            
            for i, msg in enumerate(messages[:5], 1):
                from_addr = msg.get("from", {}).get("emailAddress", {}).get("address", "Unknown")
                subject = msg.get("subject", "No Subject")
                received = msg.get("receivedDateTime", "Unknown")
                read_status = "✓" if msg.get("isRead", False) else "●"
                
                summary_text += f"""
{i}. {read_status} From: {from_addr}
   Subject: {subject}
   Received: {received}
"""
            
            return summary_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting mailbox summary: {e}")
            raise APIError(f"Failed to get mailbox summary: {str(e)}")
    
    async def get_calendar_summary(self, user_id: str = "me") -> str:
        """Get calendar summary as a resource.
        
        Args:
            user_id: User ID or 'me' for current user
            
        Returns:
            Calendar summary as formatted text
        """
        try:
            logger.info(f"OutlookResources.get_calendar_summary - Getting calendar summary for user {user_id}")
            
            # Get upcoming events
            from datetime import datetime, timedelta
            
            start_time = datetime.utcnow().isoformat() + 'Z'
            end_time = (datetime.utcnow() + timedelta(days=7)).isoformat() + 'Z'
            
            filter_dict = {
                "start/dateTime": {"ge": start_time},
                "end/dateTime": {"le": end_time}
            }
            
            events = await self.client.list_events(
                user_id=user_id,
                filter_dict=filter_dict,
                select_fields=["subject", "start", "end", "organizer", "attendees"],
                top=10
            )
            
            events_list = events.get("value", [])
            
            summary_text = f"""
Calendar Summary (Next 7 Days):
==============================

Total Upcoming Events: {len(events_list)}

Upcoming Events:
"""
            
            for i, event in enumerate(events_list[:5], 1):
                subject = event.get("subject", "No Subject")
                start_dt = event.get("start", {}).get("dateTime", "Unknown")
                end_dt = event.get("end", {}).get("dateTime", "Unknown")
                organizer = event.get("organizer", {}).get("emailAddress", {}).get("name", "Unknown")
                attendee_count = len(event.get("attendees", []))
                
                summary_text += f"""
{i}. {subject}
   Start: {start_dt}
   End: {end_dt}
   Organizer: {organizer}
   Attendees: {attendee_count}
"""
            
            return summary_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting calendar summary: {e}")
            raise APIError(f"Failed to get calendar summary: {str(e)}")
    
    async def get_contacts_summary(self, user_id: str = "me") -> str:
        """Get contacts summary as a resource.
        
        Args:
            user_id: User ID or 'me' for current user
            
        Returns:
            Contacts summary as formatted text
        """
        try:
            logger.info(f"OutlookResources.get_contacts_summary - Getting contacts summary for user {user_id}")
            
            contacts = await self.client.list_contacts(
                user_id=user_id,
                select_fields=["displayName", "emailAddresses", "companyName", "jobTitle"],
                top=20
            )
            
            contacts_list = contacts.get("value", [])
            
            summary_text = f"""
Contacts Summary:
================

Total Contacts (showing first 20): {len(contacts_list)}

Recent Contacts:
"""
            
            for i, contact in enumerate(contacts_list[:10], 1):
                name = contact.get("displayName", "No Name")
                emails = contact.get("emailAddresses", [])
                email = emails[0].get("address", "No Email") if emails else "No Email"
                company = contact.get("companyName", "")
                title = contact.get("jobTitle", "")
                
                summary_text += f"""
{i}. {name}
   Email: {email}
   Company: {company}
   Title: {title}
"""
            
            return summary_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting contacts summary: {e}")
            raise APIError(f"Failed to get contacts summary: {str(e)}")
    
    async def get_message_content(self, message_id: str, user_id: str = "me") -> str:
        """Get specific message content as a resource.
        
        Args:
            message_id: Message ID
            user_id: User ID or 'me' for current user
            
        Returns:
            Message content as formatted text
        """
        try:
            logger.info(f"OutlookResources.get_message_content - Getting content for message {message_id}")
            
            message = await self.client.get_message(message_id, user_id)
            
            from_info = message.get("from", {}).get("emailAddress", {})
            to_recipients = message.get("toRecipients", [])
            cc_recipients = message.get("ccRecipients", [])
            
            content_text = f"""
Email Message:
=============

From: {from_info.get('name', '')} <{from_info.get('address', '')}>
To: {', '.join([r.get('emailAddress', {}).get('address', '') for r in to_recipients])}
"""
            
            if cc_recipients:
                content_text += f"CC: {', '.join([r.get('emailAddress', {}).get('address', '') for r in cc_recipients])}\n"
            
            content_text += f"""
Subject: {message.get('subject', 'No Subject')}
Date: {message.get('receivedDateTime', 'Unknown')}
Importance: {message.get('importance', 'normal')}
Read: {'Yes' if message.get('isRead', False) else 'No'}

Body:
-----
"""
            
            body = message.get("body", {})
            if body:
                content_text += body.get("content", "No content available")
            else:
                content_text += "No content available"
            
            return content_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting message content: {e}")
            raise APIError(f"Failed to get message content: {str(e)}")
