"""OneDrive resources for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.graph_client import GraphClient
from ..utils.exceptions import APIError
from ..utils.helpers import format_file_size

logger = logging.getLogger(__name__)


class OneDriveResources:
    """Resources for OneDrive data exposure."""
    
    def __init__(self, graph_client: GraphClient):
        """Initialize OneDrive resources.
        
        Args:
            graph_client: Microsoft Graph client
        """
        self.client = graph_client
    
    async def get_drive_overview(self, user_id: str = "me") -> str:
        """Get OneDrive overview as a resource.
        
        Args:
            user_id: User ID or 'me' for current user
            
        Returns:
            Drive overview as formatted text
        """
        try:
            logger.info(f"OneDriveResources.get_drive_overview - Getting drive overview for user {user_id}")
            
            drive_info = await self.client.get_drive(user_id)
            
            quota = drive_info.get("quota", {})
            total = quota.get("total", 0)
            used = quota.get("used", 0)
            remaining = quota.get("remaining", 0)
            deleted = quota.get("deleted", 0)
            
            usage_percent = (used / total * 100) if total > 0 else 0
            
            overview_text = f"""
OneDrive Overview:
=================

Drive Information:
- Name: {drive_info.get('name', 'N/A')}
- Type: {drive_info.get('driveType', 'N/A')}
- Owner: {drive_info.get('owner', {}).get('user', {}).get('displayName', 'N/A')}
- ID: {drive_info.get('id', 'N/A')}

Storage Quota:
- Total Space: {format_file_size(total)}
- Used Space: {format_file_size(used)} ({usage_percent:.1f}%)
- Available Space: {format_file_size(remaining)}
- Deleted Items: {format_file_size(deleted)}

Web URL: {drive_info.get('webUrl', 'N/A')}
"""
            
            return overview_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting drive overview: {e}")
            raise APIError(f"Failed to get drive overview: {str(e)}")
    
    async def get_recent_files(self, user_id: str = "me", limit: int = 10) -> str:
        """Get recent files as a resource.
        
        Args:
            user_id: User ID or 'me' for current user
            limit: Number of files to include
            
        Returns:
            Recent files list as formatted text
        """
        try:
            logger.info(f"OneDriveResources.get_recent_files - Getting recent files for user {user_id}")
            
            # Get root folder items
            items_info = await self.client.list_drive_items(
                item_id="root",
                user_id=user_id,
                select_fields=[
                    "id", "name", "size", "lastModifiedDateTime", 
                    "file", "folder", "webUrl", "lastModifiedBy"
                ]
            )
            
            items_data = items_info.get("value", [])
            
            # Filter only files and sort by modification date
            files = [item for item in items_data if "file" in item]
            files.sort(key=lambda x: x.get("lastModifiedDateTime", ""), reverse=True)
            
            files_text = f"""
Recent Files in OneDrive:
========================

Total Files in Root: {len(files)}
Showing Most Recent {min(limit, len(files))} Files:

"""
            
            for i, file_item in enumerate(files[:limit], 1):
                name = file_item.get("name", "Untitled")
                size = file_item.get("size", 0)
                modified = file_item.get("lastModifiedDateTime", "Unknown")
                modified_by = file_item.get("lastModifiedBy", {}).get("user", {}).get("displayName", "Unknown")
                
                file_info = file_item.get("file", {})
                mime_type = file_info.get("mimeType", "Unknown")
                
                files_text += f"""
{i}. {name}
   - Size: {format_file_size(size)}
   - Modified: {modified}
   - Modified By: {modified_by}
   - Type: {mime_type}
   - Web URL: {file_item.get('webUrl', 'N/A')}
"""
            
            return files_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting recent files: {e}")
            raise APIError(f"Failed to get recent files: {str(e)}")
    
    async def get_folder_contents(self, folder_id: str = "root", user_id: str = "me") -> str:
        """Get folder contents as a resource.
        
        Args:
            folder_id: Folder ID or 'root' for root folder
            user_id: User ID or 'me' for current user
            
        Returns:
            Folder contents as formatted text
        """
        try:
            logger.info(f"OneDriveResources.get_folder_contents - Getting contents for folder {folder_id}")
            
            items_info = await self.client.list_drive_items(
                item_id=folder_id,
                user_id=user_id,
                select_fields=[
                    "id", "name", "size", "lastModifiedDateTime", 
                    "file", "folder", "webUrl", "createdBy"
                ]
            )
            
            items_data = items_info.get("value", [])
            
            # Separate files and folders
            folders = [item for item in items_data if "folder" in item]
            files = [item for item in items_data if "file" in item]
            
            contents_text = f"""
Folder Contents:
===============

Folder ID: {folder_id}
Total Items: {len(items_data)}
- Folders: {len(folders)}
- Files: {len(files)}

Folders:
"""
            
            for i, folder in enumerate(folders, 1):
                name = folder.get("name", "Untitled")
                modified = folder.get("lastModifiedDateTime", "Unknown")
                child_count = folder.get("folder", {}).get("childCount", 0)
                
                contents_text += f"""
{i}. 📁 {name}
   - Items: {child_count}
   - Modified: {modified}
   - ID: {folder.get('id', 'N/A')}
"""
            
            contents_text += "\nFiles:\n"
            
            for i, file_item in enumerate(files, 1):
                name = file_item.get("name", "Untitled")
                size = file_item.get("size", 0)
                modified = file_item.get("lastModifiedDateTime", "Unknown")
                
                contents_text += f"""
{i}. 📄 {name}
   - Size: {format_file_size(size)}
   - Modified: {modified}
   - ID: {file_item.get('id', 'N/A')}
"""
            
            return contents_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting folder contents: {e}")
            raise APIError(f"Failed to get folder contents: {str(e)}")
    
    async def get_file_details(self, item_id: str, user_id: str = "me") -> str:
        """Get file details as a resource.
        
        Args:
            item_id: File/item ID
            user_id: User ID or 'me' for current user
            
        Returns:
            File details as formatted text
        """
        try:
            logger.info(f"OneDriveResources.get_file_details - Getting details for item {item_id}")
            
            item_info = await self.client.get_drive_item(item_id, user_id)
            
            is_file = "file" in item_info
            is_folder = "folder" in item_info
            
            details_text = f"""
{'File' if is_file else 'Folder'} Details:
{'=' * (15 if is_file else 16)}

Basic Information:
- Name: {item_info.get('name', 'N/A')}
- Type: {'File' if is_file else 'Folder'}
- Size: {format_file_size(item_info.get('size', 0))}
- Created: {item_info.get('createdDateTime', 'N/A')}
- Modified: {item_info.get('lastModifiedDateTime', 'N/A')}
- Created By: {item_info.get('createdBy', {}).get('user', {}).get('displayName', 'N/A')}
- Modified By: {item_info.get('lastModifiedBy', {}).get('user', {}).get('displayName', 'N/A')}

Location:
- ID: {item_info.get('id', 'N/A')}
- Parent Path: {item_info.get('parentReference', {}).get('path', 'N/A')}
- Web URL: {item_info.get('webUrl', 'N/A')}
"""
            
            if is_file:
                file_info = item_info.get("file", {})
                details_text += f"""
File-Specific Information:
- MIME Type: {file_info.get('mimeType', 'N/A')}
- Download URL: {item_info.get('@microsoft.graph.downloadUrl', 'N/A')}
"""
                
                hashes = file_info.get("hashes", {})
                if hashes:
                    details_text += "- File Hashes:\n"
                    for hash_type, hash_value in hashes.items():
                        details_text += f"  - {hash_type}: {hash_value}\n"
            
            elif is_folder:
                folder_info = item_info.get("folder", {})
                details_text += f"""
Folder-Specific Information:
- Child Count: {folder_info.get('childCount', 0)}
"""
            
            return details_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting file details: {e}")
            raise APIError(f"Failed to get file details: {str(e)}")
