"""SharePoint resources for Office 365 MCP Server."""

import logging
from typing import Any, Dict, List, Optional

from ..clients.sharepoint_client import SharePointClient
from ..utils.exceptions import APIError

logger = logging.getLogger(__name__)


class SharePointResources:
    """Resources for SharePoint data exposure."""
    
    def __init__(self, sharepoint_client: SharePointClient):
        """Initialize SharePoint resources.
        
        Args:
            sharepoint_client: SharePoint client
        """
        self.client = sharepoint_client
    
    async def get_site_overview(self) -> str:
        """Get SharePoint site overview as a resource.
        
        Returns:
            Site overview as formatted text
        """
        try:
            logger.info("SharePointResources.get_site_overview - Getting site overview")
            
            web_info = await self.client.get_web()
            lists_info = await self.client.get_lists()
            
            web_data = web_info.get("d", {})
            lists_data = lists_info.get("d", {}).get("results", [])
            
            overview_text = f"""
SharePoint Site Overview:
========================

Site Information:
- Title: {web_data.get('Title', 'N/A')}
- Description: {web_data.get('Description', 'N/A')}
- URL: {web_data.get('Url', 'N/A')}
- Created: {web_data.get('Created', 'N/A')}
- Last Modified: {web_data.get('LastItemModifiedDate', 'N/A')}
- Language: {web_data.get('Language', 'N/A')}
- Template: {web_data.get('WebTemplate', 'N/A')}

Lists and Libraries:
- Total Lists: {len(lists_data)}
"""
            
            # Add list summary
            for i, list_item in enumerate(lists_data[:10], 1):
                if not list_item.get("Hidden", False):
                    overview_text += f"""
{i}. {list_item.get('Title', 'Untitled')}
   - Items: {list_item.get('ItemCount', 0)}
   - Type: {list_item.get('BaseTemplate', 'Unknown')}
   - Created: {list_item.get('Created', 'N/A')}
"""
            
            return overview_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting site overview: {e}")
            raise APIError(f"Failed to get site overview: {str(e)}")
    
    async def get_list_schema(self, list_name: str) -> str:
        """Get SharePoint list schema as a resource.
        
        Args:
            list_name: List name or ID
            
        Returns:
            List schema as formatted text
        """
        try:
            logger.info(f"SharePointResources.get_list_schema - Getting schema for list {list_name}")
            
            list_info = await self.client.get_list(list_name)
            list_data = list_info.get("d", {})
            
            schema_text = f"""
SharePoint List Schema:
======================

List Information:
- Title: {list_data.get('Title', 'N/A')}
- Description: {list_data.get('Description', 'N/A')}
- ID: {list_data.get('Id', 'N/A')}
- Item Count: {list_data.get('ItemCount', 0)}
- Created: {list_data.get('Created', 'N/A')}
- Last Modified: {list_data.get('LastItemModifiedDate', 'N/A')}
- Template Type: {list_data.get('BaseTemplate', 'N/A')}
- Hidden: {'Yes' if list_data.get('Hidden', False) else 'No'}
- Allow Content Types: {'Yes' if list_data.get('AllowContentTypes', False) else 'No'}
- Enable Versioning: {'Yes' if list_data.get('EnableVersioning', False) else 'No'}

Default View URL: {list_data.get('DefaultViewUrl', 'N/A')}
"""
            
            return schema_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting list schema: {e}")
            raise APIError(f"Failed to get list schema: {str(e)}")
    
    async def get_recent_files(self, folder_path: str = "") -> str:
        """Get recent files as a resource.
        
        Args:
            folder_path: Folder path (empty for root)
            
        Returns:
            Recent files list as formatted text
        """
        try:
            logger.info(f"SharePointResources.get_recent_files - Getting recent files in {folder_path or 'root'}")
            
            files_info = await self.client.get_files(folder_path)
            files_data = files_info.get("d", {}).get("results", [])
            
            # Sort by modified date (most recent first)
            sorted_files = sorted(
                files_data,
                key=lambda x: x.get("TimeLastModified", ""),
                reverse=True
            )
            
            files_text = f"""
Recent Files in {folder_path or 'Root Folder'}:
{'=' * (20 + len(folder_path or 'Root Folder'))}

Total Files: {len(sorted_files)}

Recent Files (Last 10):
"""
            
            for i, file_item in enumerate(sorted_files[:10], 1):
                name = file_item.get("Name", "Untitled")
                size = file_item.get("Length", 0)
                modified = file_item.get("TimeLastModified", "Unknown")
                
                # Format file size
                if size > 1024 * 1024:
                    size_str = f"{size / (1024 * 1024):.1f} MB"
                elif size > 1024:
                    size_str = f"{size / 1024:.1f} KB"
                else:
                    size_str = f"{size} bytes"
                
                files_text += f"""
{i}. {name}
   - Size: {size_str}
   - Modified: {modified}
   - URL: {file_item.get('ServerRelativeUrl', 'N/A')}
"""
            
            return files_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting recent files: {e}")
            raise APIError(f"Failed to get recent files: {str(e)}")
    
    async def get_site_users(self) -> str:
        """Get site users as a resource.
        
        Returns:
            Site users list as formatted text
        """
        try:
            logger.info("SharePointResources.get_site_users - Getting site users")
            
            users_info = await self.client.get_site_users()
            users_data = users_info.get("d", {}).get("results", [])
            
            users_text = f"""
SharePoint Site Users:
=====================

Total Users: {len(users_data)}

User List:
"""
            
            for i, user in enumerate(users_data[:20], 1):
                title = user.get("Title", "Unknown")
                email = user.get("Email", "N/A")
                login_name = user.get("LoginName", "N/A")
                is_site_admin = user.get("IsSiteAdmin", False)
                
                users_text += f"""
{i}. {title}
   - Email: {email}
   - Login: {login_name}
   - Site Admin: {'Yes' if is_site_admin else 'No'}
"""
            
            return users_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting site users: {e}")
            raise APIError(f"Failed to get site users: {str(e)}")
    
    async def get_list_items_summary(self, list_name: str, limit: int = 10) -> str:
        """Get list items summary as a resource.
        
        Args:
            list_name: List name or ID
            limit: Number of items to include
            
        Returns:
            List items summary as formatted text
        """
        try:
            logger.info(f"SharePointResources.get_list_items_summary - Getting items summary for list {list_name}")
            
            items_info = await self.client.get_list_items(
                list_id=list_name,
                top=limit
            )
            
            items_data = items_info.get("d", {}).get("results", [])
            
            summary_text = f"""
List Items Summary: {list_name}
{'=' * (20 + len(list_name))}

Total Items (showing first {limit}): {len(items_data)}

Items:
"""
            
            for i, item in enumerate(items_data, 1):
                title = item.get("Title", f"Item {item.get('Id', 'Unknown')}")
                created = item.get("Created", "Unknown")
                modified = item.get("Modified", "Unknown")
                author = item.get("Author", {}).get("Title", "Unknown") if item.get("Author") else "Unknown"
                
                summary_text += f"""
{i}. {title}
   - ID: {item.get('Id', 'N/A')}
   - Created: {created}
   - Modified: {modified}
   - Author: {author}
"""
            
            return summary_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting list items summary: {e}")
            raise APIError(f"Failed to get list items summary: {str(e)}")
